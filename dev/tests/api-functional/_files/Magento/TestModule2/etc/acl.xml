<?xml version="1.0"?>
<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:Acl/etc/acl.xsd">
    <acl>
        <resources>
            <resource id="Magento_Backend::admin">
                <resource id="Magento_TestModule2::all" title="TestModule2" sortOrder="1">
                    <resource id="Magento_TestModule2::resource1" title="Resource1" sortOrder="20"/>
                    <resource id="Magento_TestModule2::resource2" title="Resource2" sortOrder="10"/>
                    <resource id="Magento_TestModule2::resource3" title="Resource3" sortOrder="30"/>
                </resource>
            </resource>
        </resources>
    </acl>
</config>
