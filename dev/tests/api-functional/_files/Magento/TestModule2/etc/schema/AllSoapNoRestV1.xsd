<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema">
    <xsd:complexType name="ItemRequest">
        <xsd:annotation>
            <xsd:documentation/>
            <xsd:appinfo xmlns:inf="http://magento.ll/webapi/soap"/>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="id" type="xsd:int">
                <xsd:annotation>
                    <xsd:documentation>Entity ID</xsd:documentation>
                    <xsd:appinfo xmlns:inf="http://magento.ll/webapi/soap">
                        <inf:min/>
                        <inf:max/>
                        <inf:callInfo>
                            <inf:callName>Item</inf:callName>
                            <inf:requiredInput>Yes</inf:requiredInput>
                        </inf:callInfo>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
        </xsd:sequence>
    </xsd:complexType>
    <xsd:complexType name="ItemResponse">
        <xsd:annotation>
            <xsd:documentation>Response container for the Item call.</xsd:documentation>
            <xsd:appinfo xmlns:inf="http://magento.ll/webapi/soap"/>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="id" type="xsd:string">
                <xsd:annotation>
                    <xsd:documentation>Default label</xsd:documentation>
                    <xsd:appinfo xmlns:inf="http://magento.ll/webapi/soap">
                        <inf:maxLength/>
                        <inf:callInfo>
                            <inf:callName>Item</inf:callName>
                            <inf:returned>Always</inf:returned>
                        </inf:callInfo>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="ItemsResponse">
        <xsd:annotation>
            <xsd:documentation>Response container for the Items call.</xsd:documentation>
            <xsd:appinfo xmlns:inf="http://magento.ll/webapi/soap"/>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element minOccurs="0" maxOccurs="unbounded" name="complexObjectArray"
                         type="ItemsArray">
                <xsd:annotation>
                    <xsd:documentation>Default label</xsd:documentation>
                    <xsd:appinfo xmlns:inf="http://magento.ll/webapi/soap">
                        <inf:maxLength/>
                        <inf:callInfo>
                            <inf:callName>Items</inf:callName>
                            <inf:returned>Always</inf:returned>
                        </inf:callInfo>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
        </xsd:sequence>
    </xsd:complexType>


    <xsd:complexType name="ItemsArray">
        <xsd:annotation>
            <xsd:documentation>Response container for the Items call.</xsd:documentation>
            <xsd:appinfo xmlns:inf="http://magento.ll/webapi/soap"/>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="id" type="xsd:string">
                <xsd:annotation>
                    <xsd:documentation>Default label</xsd:documentation>
                    <xsd:appinfo xmlns:inf="http://magento.ll/webapi/soap">
                        <inf:maxLength/>
                        <inf:callInfo>
                            <inf:callName>Items</inf:callName>
                            <inf:returned>Always</inf:returned>
                        </inf:callInfo>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="name"  type="xsd:string">
                <xsd:annotation>
                    <xsd:documentation>Default label</xsd:documentation>
                    <xsd:appinfo xmlns:inf="http://magento.ll/webapi/soap">
                        <inf:maxLength/>
                        <inf:callInfo>
                            <inf:callName>Item</inf:callName>
                            <inf:returned>Always</inf:returned>
                        </inf:callInfo>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
        </xsd:sequence>
    </xsd:complexType>

    <xsd:complexType name="CreateRequest">
        <xsd:annotation>
            <xsd:documentation/>
            <xsd:appinfo xmlns:inf="http://magento.ll/webapi/soap"/>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="name"  type="xsd:string">
                <xsd:annotation>
                    <xsd:documentation>Default label</xsd:documentation>
                    <xsd:appinfo xmlns:inf="http://magento.ll/webapi/soap">
                        <inf:maxLength/>
                        <inf:callInfo>
                            <inf:callName>Create</inf:callName>
                            <inf:returned>Always</inf:returned>
                        </inf:callInfo>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
        </xsd:sequence>
    </xsd:complexType>
    <xsd:complexType name="CreateResponse">
        <xsd:annotation>
            <xsd:documentation>Response container for the Create call.</xsd:documentation>
            <xsd:appinfo xmlns:inf="http://magento.ll/webapi/soap"/>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="id" type="xsd:string">
                <xsd:annotation>
                    <xsd:documentation>Default label</xsd:documentation>
                    <xsd:appinfo xmlns:inf="http://magento.ll/webapi/soap">
                        <inf:maxLength/>
                        <inf:callInfo>
                            <inf:callName>Create</inf:callName>
                            <inf:returned>Always</inf:returned>
                        </inf:callInfo>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="name"  type="xsd:string">
                <xsd:annotation>
                    <xsd:documentation>Default label</xsd:documentation>
                    <xsd:appinfo xmlns:inf="http://magento.ll/webapi/soap">
                        <inf:maxLength/>
                        <inf:callInfo>
                            <inf:callName>Create</inf:callName>
                            <inf:returned>Always</inf:returned>
                        </inf:callInfo>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
        </xsd:sequence>
    </xsd:complexType>
    <xsd:complexType name="UpdateRequest">
        <xsd:annotation>
            <xsd:documentation/>
            <xsd:appinfo xmlns:inf="http://magento.ll/webapi/soap"/>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="id" type="xsd:int">
                <xsd:annotation>
                    <xsd:documentation>Entity ID</xsd:documentation>
                    <xsd:appinfo xmlns:inf="http://magento.ll/webapi/soap">
                        <inf:min/>
                        <inf:max/>
                        <inf:callInfo>
                            <inf:callName>Update</inf:callName>
                            <inf:requiredInput>Yes</inf:requiredInput>
                        </inf:callInfo>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
        </xsd:sequence>
    </xsd:complexType>
    <xsd:complexType name="UpdateResponse">
        <xsd:annotation>
            <xsd:documentation>Response container for the Update call.</xsd:documentation>
            <xsd:appinfo xmlns:inf="http://magento.ll/webapi/soap"/>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="id" type="xsd:string">
                <xsd:annotation>
                    <xsd:documentation>Default label</xsd:documentation>
                    <xsd:appinfo xmlns:inf="http://magento.ll/webapi/soap">
                        <inf:maxLength/>
                        <inf:callInfo>
                            <inf:callName>Update</inf:callName>
                            <inf:returned>Always</inf:returned>
                        </inf:callInfo>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
        </xsd:sequence>
    </xsd:complexType>
    <xsd:complexType name="RemoveRequest">
        <xsd:annotation>
            <xsd:documentation/>
            <xsd:appinfo xmlns:inf="http://magento.ll/webapi/soap"/>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="id" type="xsd:int">
                <xsd:annotation>
                    <xsd:documentation>Entity ID</xsd:documentation>
                    <xsd:appinfo xmlns:inf="http://magento.ll/webapi/soap">
                        <inf:min/>
                        <inf:max/>
                        <inf:callInfo>
                            <inf:callName>Remove</inf:callName>
                            <inf:requiredInput>Yes</inf:requiredInput>
                        </inf:callInfo>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
        </xsd:sequence>
    </xsd:complexType>
    <xsd:complexType name="RemoveResponse">
        <xsd:annotation>
            <xsd:documentation>Response container for the Remove call.</xsd:documentation>
            <xsd:appinfo xmlns:inf="http://magento.ll/webapi/soap"/>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="id" type="xsd:string">
                <xsd:annotation>
                    <xsd:documentation>Default label</xsd:documentation>
                    <xsd:appinfo xmlns:inf="http://magento.ll/webapi/soap">
                        <inf:maxLength/>
                        <inf:callInfo>
                            <inf:callName>Remove</inf:callName>
                            <inf:returned>Always</inf:returned>
                        </inf:callInfo>
                    </xsd:appinfo>
                </xsd:annotation>
            </xsd:element>
        </xsd:sequence>
    </xsd:complexType>
</xsd:schema>
