<?xml version="1.0"?>
<!--
/**
 * Mageplaza
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Mageplaza.com license that is
 * available through the world-wide-web at this URL:
 * https://www.mageplaza.com/LICENSE.txt
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade this extension to newer
 * version in the future.
 *
 * @category    Mageplaza
 * @package     Mageplaza_Blog
 * @copyright   Copyright (c) Mageplaza (https://www.mageplaza.com/)
 * @license     https://www.mageplaza.com/LICENSE.txt
 */
-->
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" layout="admin-2columns-left" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <update handle="editor"/>
    <body>
        <referenceContainer name="content">
            <block class="Mageplaza\Blog\Block\Adminhtml\Author\Edit" name="mageplaza_blog_author_edit"/>
        </referenceContainer>
        <referenceContainer name="left">
            <block class="Mageplaza\Blog\Block\Adminhtml\Author\Edit\Tabs" name="mageplaza_blog_author_tabs">
                <block class="Mageplaza\Blog\Block\Adminhtml\Author\Edit\Tab\Author" name="mageplaza_blog_author_edit_tab_author"/>
                <block class="Mageplaza\Blog\Block\Adminhtml\Author\Edit\Tab\Post" name="mageplaza_blog_author_edit_tab_post"/>
                <action method="addTab">
                    <argument name="name" xsi:type="string">author</argument>
                    <argument name="block" xsi:type="string">mageplaza_blog_author_edit_tab_author</argument>
                </action>
                <action method="addTab">
                    <argument name="name" xsi:type="string">post</argument>
                    <argument name="block" xsi:type="string">mageplaza_blog_author_edit_tab_post</argument>
                </action>
            </block>
        </referenceContainer>
    </body>
</page>
