/**
 * Mageplaza
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Mageplaza.com license that is
 * available through the world-wide-web at this URL:
 * https://www.mageplaza.com/LICENSE.txt
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade this extension to newer
 * version in the future.
 *
 * @category    Mageplaza
 * @package     Mageplaza_Blog
 * @copyright   Copyright (c) Mageplaza (https://www.mageplaza.com/)
 * @license     https://www.mageplaza.com/LICENSE.txt
 */
.mp-blog-view a,
.mp-sidebar a {
  text-decoration: none;
}

.mp-blog-rss {
  text-align: right;
}

.mp-post-info {
  color: #999999;
  font-size: 14px;
  line-height: 25px;
  margin-bottom: 5px;

  span {
    color: #6f6f6f;
    ;
  }
}

.post-item-wraper {
  border: 1px solid #eee;

  &:hover {
    border: 1px solid #eee;
    box-shadow: 3px 3px 4px 0 rgba(0, 0, 0, 0.3);
  }
}

.post-info-wraper {
  padding: 20px 15px 10px 15px;
}

.post-list-item {
  margin-bottom: 20px;
}

.post-short-description {
  max-width: 100%;

  p {
    max-width: 100%;
    //overflow: hidden;
    //text-overflow: ellipsis;
    //max-height: 20px;
  }
}

.post-post_content {
  margin: 10px 0 10px 0;
}

@media (min-width: 768px) {
  .post-sharing-button {
    text-align: center;
  }
}

#sharing {
  padding: 20px 30px;
  background: #f6f6f6;
  margin-top: 40px;
}

@media (min-width: 768px) {
  #sharing .share-col-left {
    float: left;
    width: 20%;
  }
}

.about-admin {
  h4.admin-title {
    width: 215px;
    float: left;
  }
}

.block-blog-related {
  margin-top: 35px;

  .related-content-container {
    padding: 0 10px;

    .author-content-image {
      float: left;
      width: 135px;
    }

    .author-content-information {
      float: left;
      padding-left: 20px;
      width: calc(~"100% - 155px");

      .author-name {
        font-size: 20px;
      }
    }
  }
}

#post-reviews {
  margin-top: 50px;
}

a.mp-relate-link {
  background: #f55567;
  color: #0a0a0a;

  img.img-responsive {
    margin: 0 auto;
    height: 200px;
    display: block;
  }
}

.menu-archives {
  list-style: none;
  padding-left: 8px;

  a.list-archives {
    color: inherit;
  }
}

.author-image {
  text-align: center;

  img.img-responsive {
    border: none;
    padding-right: 7px;
    padding-left: 7px;
    margin-top: 15px;
    margin-bottom: 20px;
  }
}

.tab-content {
  border: 1px solid #ddd;
  border-top: none;
}

.pager {
  text-align: center;
}

.pager span.toolbar-number {
  display: none;
}

.block-title p {
  font-size: 18px;
  font-weight: 300;
}

.mp-sidebar {
  margin-bottom: 25px;

  .block-content {
    padding-left: 10px;
  }

  .data.item.title {
    cursor: pointer;
  }

  .product.data.items {
    >.item.content {
      padding: 20px 0 20px 10px !important;
    }
  }
}

.media-width(@extremum, @break) when (@extremum ='min') and (@break =@screen__m) {
  .mp-sidebar {
    .data.item.title {
      width: 50% !important;
      margin: 0 !important;

      .data.switch {
        padding-left: 10px !important;
        padding-right: 10px !important;

        div {
          width: fit-content;
          margin: auto;
        }
      }
    }

    #tab-label-popular {
      .data.switch {
        border-right: 0 !important;
      }
    }
  }
}

.sidebar-tags {
  margin-bottom: 15px;
}

a.label.label-default.tags-list {
  background: #fff;
  border: 1px solid #bbb;
  color: inherit;
  font-weight: normal;
  font-size: 100%;
  border-radius: 1px;
  display: inline-block;
  margin-bottom: 5px;
  padding: 10px;
  margin-right: 5px
}

ul.menu-categories {
  li.category-item {
    list-style: none;
    //padding: 10px 0 5px 0;
  }

  .category-level3 {
    display: none;
    padding-left: 15px;
  }

  .category-level4 {
    display: none;
    padding-left: 30px;
  }

  a.list-categories {
    color: inherit;
    margin-left: 8px;
  }

  i {
    cursor: pointer;
  }
}

h2.list-title {
  margin-bottom: 30px;
}

.list-post-tabs {
  padding: 5px 10px 5px 0;

  &:hover {
    background-color: #eee;
  }

  .post-left {
    float: left;
    width: 25%;
  }

  .post-right {
    float: left;
    width: 75%;
    padding-left: 10px;
    box-sizing: border-box;

    span {
      font-size: 14px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      display: block;

      &.create-at {
        font-size: 12px;
      }
    }
  }
}

.tab-pane.fade {
  border-left: 1px solid #ddd;
  border-right: 1px solid #ddd;
  border-bottom: 1px solid #ddd;
  margin-bottom: 20px;
}

.fa.fa-angle-right {
  font-size: larger;
}

.sidebar .mpblog-search {
  float: none !important;
  padding: 0 !important;
  margin-bottom: 15px !important;
  width: 100%;
}

.autocomplete-suggestions {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  border: 1px solid #ddd;
  background: #FFF;
  cursor: default;
  overflow: auto;
  -webkit-box-shadow: 1px 4px 3px rgba(50, 50, 50, 0.64);
  -moz-box-shadow: 1px 4px 3px rgba(50, 50, 50, 0.64);
  box-shadow: 1px 4px 3px rgba(50, 50, 50, 0.64);
  border-top: 0;
  padding: 10px 0;
  overflow-x: hidden;

  strong {
    font-weight: bold;
    color: #000;
  }
}

.autocomplete-suggestion {
  padding: 2px 5px;
  border-bottom: 1px solid #eee;

  &:last-child {
    border-bottom: none;
  }

  .mpblog-suggestion {
    display: block;

    &:after {
      content: ".";
      display: block;
      clear: both;
      visibility: hidden;
      line-height: 0;
      height: 0;
    }

    .mpblog-suggestion-left,
    .mpblog-suggestion-right {
      float: left;
      padding: 5px 10px;
    }

    .mpblog-suggestion-left {
      max-width: 25%;
    }

    .mpblog-suggestion-right.image-visible {
      max-width: calc(~"75% - 40px");
    }
  }
}

.autocomplete-no-suggestion {
  padding: 2px 5px;
}

.autocomplete-selected {
  background: #F0F0F0;
}

.autocomplete-group {
  padding: 2px 5px;
  font-weight: bold;
  font-size: 16px;
  color: #000;
  display: block;
  border-bottom: 1px solid #000;
}

//@media (max-width: 767px) {
//  .autocomplete-suggestion .mpblog-suggestion .mpblog-suggestion-right {
//    max-width: calc(100% - 31%);
//  }
//}

.mpblog-product-des {
  color: #333333;

  p {
    margin: 0;
    font-size: 12px;
  }
}

.mpblog-search {
  position: relative;

  button.action.search {
    display: inline-block;
    background: none !important;
    -moz-box-sizing: content-box;
    border: 0;
    box-shadow: none;
    line-height: inherit;
    margin: 0;
    padding: 0;
    text-shadow: none;
    font-weight: 400;
    position: absolute;
    right: 10px;
    top: 2px;
    z-index: 1;

    :before {
      -webkit-font-smoothing: antialiased;
      color: #8f8f8f;
    }

    span {
      border: 0;
      clip: rect(0, 0, 0, 0);
      height: 1px;
      margin: -1px;
      overflow: hidden;
      padding: 0;
      position: absolute;
      width: 1px;
    }
  }

  label[for="mpblog-search-box"] {
    display: none;
  }
}

/* style comment */
textarea.default-cmt__content__cmt-block__cmt-box__cmt-input {
  overflow: hidden !important;
  resize: none;
  min-height: 80px;
  border-radius: 4px;
  margin-bottom: 15px;
}

.default-cmt__content__cmt-block__guest-box {
  margin-bottom: 20px;
}

.show-cmt__cmt-row.is-reply {
  padding-left: 50px !important;
}

.default-cmt__content__cmt-block__cmt-box__cmt-btn {
  float: right;

  .default-cmt_loading {
    display: inline-block;
    width: 20px;
    vertical-align: middle;
    margin-right: 5px;
  }
}

.cmt-row__reply-row {
  margin-top: 10px;
}

.default-cmt__cmt-login {
  float: right;
  margin-top: 20px;
  margin-bottom: 10px;
}

div.default-cmt__content__cmt-content {
  width: 100%;
  margin-top: 50px;
  float: left;
}

ul.default-cmt__content__cmt-content {
  list-style: none !important;
}

.cmt-row__cmt-content {
  font-size: 13px;
}

.cmt-row__cmt-interactions {
  font-size: 13px;
}

.cmt-row__cmt-content p {
  margin: 3px 0 !important;
}

.interactions__btn-actions {
  display: inline-block;
  margin-right: 10px;
}

a.interactions__btn-actions.action {
  cursor: pointer;
}

.interactions__cmt-createdat {
  display: inline-block;
  color: #999;
  font-size: 13px;
}

.cmt-row.reply-row {
  margin-bottom: 0 !important;
}

.reply-form__form-input {
  margin-bottom: 0 !important;
  position: relative;

  .default-cmt_loading {
    width: 20px;
    position: absolute;
    top: 5px;
    right: 5px;
  }
}

.default-cmt__content__cmt-row.reply-row {
  margin-top: 0 !important;
}

li.default-cmt__content__cmt-content__cmt-row.cmt-row.reply-row {
  margin-top: 5px;
  padding-bottom: 0;
}

li.default-cmt__content__cmt-content__cmt-row.cmt-row {
  margin-top: 5px;
  margin-bottom: 15px;
  padding-bottom: 5px;
  border-top: 1px solid #f5f5f5;
}

.default-cmt__content__cmt-content ul:first-child {
  padding-left: 5px !important;
}

/* end style comment */

.widget-title {
  margin-bottom: 20px;
}

.author-social img.img-responsive {
  float: left;
  margin-top: 15px;
  margin-left: 15px;
  height: 25px;
  width: 25px;
}

.share-col-right {
  display: inline-block;
}

/*post view css*/
.page-layout-1column .column.main {
  width: 100% !important;
}

/* Footer Copyright */
.mp-footer {
  margin-top: 50px;
  font-size: 8px !important;
  display: block;
  clear: both;
  float: right;
  text-transform: inherit;

  h3 {
    margin-top: 50px;
    font-size: 8px !important;
    display: block;
    clear: both;
    float: right;
    text-transform: inherit;
  }
}

.mp-footer-links-light {
  color: #E8E8E8 !important;
  margin: 0;
  padding: 0;
  border: 0;
  vertical-align: baseline;

  a {
    color: #E8E8E8 !important;
  }
}

.post-image {
  min-height: 250px;
  position: relative;

  img.img-responsive {
    max-height: 100%;
    max-width: 100%;
    position: absolute;
    right: 0;
    top: 0;
    left: 0;
    bottom: 0;
    margin: auto;
    border: none;
  }
}

.breadcrumbs {
  margin-bottom: 0;
}

ul.panel-menu a {
  color: white;
  font-size: 14px;
}

.mpblog-post-view {
  .post-view-image {
    text-align: center;
  }
}

.post-item-wraper {
  padding: 10px !important;
}

select#mpc-category-select {
  padding-inline: 10px;
  padding-block: 7px;
  width: 30%;
  text-align-last: left;
  background: white;
  border: 2px solid #e3e4e6;
  border-radius: 3px;
  color: #000;
}

@media only screen and (min-width: 600px) {
  .post-item-list {
    .post-image {
      min-width: 250px;
      display: table-cell;
    }

    .post-info-wraper {
      display: table-cell;
      vertical-align: top;
    }
  }
}

.post-info-wraper {
  display: table-cell;
  vertical-align: top;

  h2.mp-post-title {
    margin-top: 0;
  }
}

.post-list-content {
  .limiter {
    display: block !important;
  }
}

/** Yourstore theme */
.mpblog-etheme-yourstore {

  .post-list-container,
  .post-view,
  .mp-blog-rss {
    width: 85%;
    margin: auto;
  }

  .block-blog-related {
    border-bottom: solid 1px #dfdfdf;
  }
}

@media (min-width: 768px) {
  .mpblog-etheme-yourstore {
    .page-layout-2columns-right .post-list-content {
      border-right: solid 1px #dfdfdf;
    }

    .page-layout-2columns-left .post-list-content {
      border-left: solid 1px #dfdfdf;
    }
  }
}

@media (min-width: 1024px) {
  .mpblog-etheme-yourstore {
    .products-same-post li {
      height: 400px;
      width: 24.5% !important;
    }
  }
}

@media (min-width: 1440px) {
  .mpblog-etheme-yourstore {
    .products-same-post li {
      height: 500px;
      width: 16.67% !important;
    }
  }
}

@media all and (min-width: 640px) {
  .post-list-content .products-grid .product-item {
    width: 93%;
    padding: 0;
  }
}

@media all and (max-width: 640px) {

  .post-list-content .products-grid .product-item {
    width: 98%;
  }
}

.mpblog-post-index {

  img.blog-banner {
    margin-left: -20%;
  }

  .sidebar.sidebar-main {
    order: 0;
  }

  .columns {
    display: flex;
  }

  .item.product.product-item.post-list-item.post-item-grid {
    height: max-content !important;
  }

  .page-title-wrapper,
  .mp-blog-view .mp-post-info,
  .mp-blog-view hr {
    display: none;
  }

  .column.main {
    width: 100% !important;
  }

  .products-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
  }

  .product-item:nth-child(2n + 1) {
    margin-left: 2%;
  }

  .sidebar.sidebar-additional {
    display: none;
  }
}

.mpblog-category-view {
  .blog-banner {
    display: none;
  }
}

.mpblog-post-view {
  a.mp-info {
    font-size: 14px;
  }

  .sidebar.sidebar-main {
    display: none;
  }

  .columns,
  .column.main {
    width: 100% !important;
  }

  .sidebar.sidebar-additional {
    display: none;
  }

  div#mpblog-list-container {
    width: 90%;
  }

  .post-view-image {
    img.img-responsive {
      max-height: 320px;
      width: 100%;
      object-fit: cover;
    }
  }
}

@media all and (max-width:1024px) {
  .mpblog-post-index {
    .item.product.product-item.post-list-item.post-item-grid {
      padding: 0 !important;
    }

    img.blog-banner {
      width: 100%;
      margin-left: 0;
    }

    .products-grid {
      display: grid;
      grid-template-columns: repeat(1, 1fr);
    }
  }
}

@media all and (min-width: 768px) {
  .mpblog-post-view {
    .post-post_content img {
      float: right;
      margin: 0 0 10px 10px;
      max-width: 489px;
      border-radius: 15px;
    }
  }
}

@media all and (max-width: 767px) {
  .mpblog-post-view {
    .post-post_content img {
      float: none;
      margin: 0 auto 15px auto;
      max-width: 100%;
      border-radius: 15px;
    }
  }
}

/* mageplaza end blog style */