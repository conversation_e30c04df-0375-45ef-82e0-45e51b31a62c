<?xml version="1.0"?>
<!--
/**
 * Mageplaza
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Mageplaza.com license that is
 * available through the world-wide-web at this URL:
 * https://www.mageplaza.com/LICENSE.txt
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade this extension to newer
 * version in the future.
 *
 * @category    Mageplaza
 * @package     Mageplaza_Blog
 * @copyright   Copyright (c) Mageplaza (https://www.mageplaza.com/)
 * @license     https://www.mageplaza.com/LICENSE.txt
 */
-->
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" layout="1column" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <update handle="mpblog_author"/>
    <body>
        <referenceContainer name="content">
            <block class="Mageplaza\Blog\Block\Post\AuthorPost" name="mpblog.author.post" as="mpblog_post" template="Mageplaza_Blog::post/author_post.phtml" cacheable="false">
                <block class="Mageplaza\Blog\Block\Post\ManagePost" name="mpblog.manage.post" as="mpblog_manage_post" template="Mageplaza_Blog::post/manage_post.phtml">
                    <block name="field_additional"/>
                </block>
            </block>
        </referenceContainer>
    </body>
</page>

