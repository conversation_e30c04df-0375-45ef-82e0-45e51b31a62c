<?xml version="1.0"?>
<!--
/**
 * Mageplaza
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Mageplaza.com license that is
 * available through the world-wide-web at this URL:
 * https://www.mageplaza.com/LICENSE.txt
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade this extension to newer
 * version in the future.
 *
 * @category    Mageplaza
 * @package     Mageplaza_Blog
 * @copyright   Copyright (c) Mageplaza (https://www.mageplaza.com/)
 * @license     https://www.mageplaza.com/LICENSE.txt
 */
-->
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
        <referenceBlock name="customer_account_navigation">
            <block class="Magento\Customer\Block\Account\SortLinkInterface" name="author-link" ifconfig="blog/general/enabled">
                <arguments>
                    <argument name="path" xsi:type="string">mpblog/author/signup</argument>
                    <argument name="label" xsi:type="string">My Better Blog</argument>
                    <argument name="sortOrder" xsi:type="number">200</argument>
                </arguments>
            </block>
        </referenceBlock>
        <referenceBlock name="footer_links">
            <block class="Mageplaza\Blog\Block\Html\Footer" name="mp_blog_footer_link" template="Mageplaza_Blog::html/footer.phtml" after="-"/>
        </referenceBlock>
    </body>
</page>
