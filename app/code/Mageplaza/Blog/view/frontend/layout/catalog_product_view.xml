<?xml version="1.0"?>
<!--
/**
 * Mageplaza
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Mageplaza.com license that is
 * available through the world-wide-web at this URL:
 * https://www.mageplaza.com/LICENSE.txt
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade this extension to newer
 * version in the future.
 *
 * @category    Mageplaza
 * @package     Mageplaza_Blog
 * @copyright   Copyright (c) Mageplaza (https://www.mageplaza.com/)
 * @license     https://www.mageplaza.com/LICENSE.txt
 */
-->
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <head>
        <css src="Mageplaza_Core::css/owl.carousel.css"/>
        <css src="Mageplaza_Core::css/owl.theme.css"/>
    </head>
    <body>
        <referenceBlock name="product.info.details">
            <block class="Mageplaza\Blog\Block\Post\RelatedPost" name="related.post.tab" template="Mageplaza_Blog::post/related_post.phtml" group="detailed_info" ifconfig="blog/product_post/product_detail/enable_post"/>
        </referenceBlock>
    </body>
</page>
