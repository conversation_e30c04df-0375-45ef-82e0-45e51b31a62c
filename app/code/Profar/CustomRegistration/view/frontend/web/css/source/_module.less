.modal-custom-registration {
    .modal-inner-wrap {
        max-width: 500px;
    }

    .modal-content {
        padding: 20px;
    }

    h2 {
        text-align: center;
        margin-bottom: 30px;
    }

    .social-login-options {
        margin-bottom: 30px;

        .social-login {
            width: 100%;
            margin-bottom: 15px;
            padding: 12px;
            border-radius: 4px;
            font-size: 16px;
            display: flex;
            align-items: center;
            justify-content: center;

            &::before {
                content: "";
                display: inline-block;
                width: 24px;
                height: 24px;
                margin-right: 10px;
                background-size: contain;
                background-repeat: no-repeat;
            }

            &.social-login-google {
                background-color: #fff;
                color: #757575;
                border: 1px solid #ddd;

                &::before {
                    background-image: url("../images/google-icon.png");
                }

                &:hover {
                    background-color: #f5f5f5;
                }
            }

            &.social-login-facebook {
                background-color: #1877f2;
                color: #fff;
                border: none;

                &::before {
                    background-image: url("../images/facebook-icon.png");
                }

                &:hover {
                    background-color: #166fe5;
                }
            }
        }
    }

    .separator {
        text-align: center;
        margin: 20px 0;
        position: relative;

        &::before,
        &::after {
            content: "";
            position: absolute;
            top: 50%;
            width: 45%;
            height: 1px;
            background-color: #ddd;
        }

        &::before {
            left: 0;
        }

        &::after {
            right: 0;
        }

        span {
            background-color: #fff;
            padding: 0 15px;
            color: #666;
            text-transform: uppercase;
            font-size: 14px;
        }
    }

    .email-signup {
        width: 100%;
        padding: 12px;
        font-size: 16px;
    }

    .password-strength-meter {
        margin: 15px 0;

        .password-strength-meter-track {
            height: 4px;
            background-color: #e8e8e8;
            border-radius: 2px;
            overflow: hidden;
        }

        .password-strength-meter-fill {
            height: 100%;
            background-color: #ff0000;
            transition: width 0.3s ease, background-color 0.3s ease;

            &[style*="width: 25%"] {
                background-color: #ff0000;
            }

            &[style*="width: 50%"] {
                background-color: #ffa500;
            }

            &[style*="width: 75%"] {
                background-color: #ffff00;
            }

            &[style*="width: 100%"] {
                background-color: #00ff00;
            }
        }

        .password-strength-meter-label {
            margin-top: 5px;
            font-size: 12px;
            color: #666;
        }
    }

    .password-requirements {
        margin: 15px 0;
        padding: 10px;
        background-color: #f8f8f8;
        border-radius: 4px;

        ul {
            list-style: none;
            padding: 0;
            margin: 0;

            li {
                margin: 5px 0;
                color: #666;
                position: relative;
                padding-left: 20px;

                &::before {
                    content: "×";
                    position: absolute;
                    left: 0;
                    color: #ff0000;
                }

                &.valid {
                    color: #008000;

                    &::before {
                        content: "✓";
                        color: #008000;
                    }
                }
            }
        }
    }

    .actions-toolbar {
        margin-top: 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .primary {
            .action {
                width: 100%;
            }
        }

        .secondary {
            .action.back {
                color: #666;
                text-decoration: none;

                &:hover {
                    color: #333;
                }
            }
        }
    }

    .field {
        margin-bottom: 20px;

        .label {
            margin-bottom: 8px;
        }

        .control {
            position: relative;
        }

        .field-note {
            font-size: 13px;
            color: #666;
            margin-top: 5px;
        }
    }

    .registration-step {
        display: none;
        &.active {
            display: block;
        }
    }

    .login-options {
        display: flex;
        flex-direction: column;
        gap: 15px;
        margin-bottom: 20px;

        .action {
            width: 100%;
            padding: 12px;
            font-size: 16px;
            border-radius: 4px;
            text-align: center;
            transition: all 0.3s ease;

            &.primary {
                background-color: #1979c3;
                color: #fff;
                border: none;

                &:hover {
                    background-color: #006bb4;
                }
            }

            &.google-login {
                background-color: #fff;
                color: #757575;
                border: 1px solid #ddd;
                display: flex;
                align-items: center;
                justify-content: center;

                &:hover {
                    background-color: #f5f5f5;
                }

                &::before {
                    content: "";
                    display: inline-block;
                    width: 18px;
                    height: 18px;
                    margin-right: 8px;
                    background: url("../images/google-icon.png") no-repeat;
                    vertical-align: middle;
                }
            }
        }
    }
}

& when (@media-common =true) {
    .form-login {
        input {
            padding-inline: 10px;
        }
    }

    button.action.reload.captcha-reload {
        background-color: #00a4e8;
        padding: 10px !important;
        border-radius: 8px;
        color: #ffffff !important;
        border: none;
    }

    .customer-account-create {
        .main.column {
            display: flex;
            flex-direction: column-reverse;
            align-items: center;
            justify-content: flex-end;

            h2 {
                display: none !important;
            }

            p {
                width: 100%;
                text-align: left;
                order: 2;
            }

            .sociallogin-wrapper {
                width: 400px !important;
                margin: 0 !important;

                .sociallogin-block-title.block-title {
                    display: none !important;
                }

                .sociallogin-padding {
                    width: 100% !important;
                    max-width: 100% !important;
                    padding: 0 20px;

                    .sociallogin-button {
                        border-radius: 14px;
                        padding: 5px;
                        box-shadow: none !important;
                        border: 1px solid #ccc;
                    }
                }
            }
        }
    }

    .custom-registration-form {
        width: 400px;
        margin-top: 32px;

        .control {

            input[type="text"],
            input[type="email"],
            input[type="password"] {
                width: 100%;
                height: 40px;
                padding: 10px;
            }
        }
    }

    .registration-form-container {
        order: 0;
        max-width: 600px;
        margin: 0 auto;
        padding: 0 20px;

        .registration-step {
            margin-bottom: 20px;
        }

        .login-options {
            display: flex;
            flex-direction: column;
            gap: 10px;

            .action {
                width: 100%;
                padding: 10px;
                text-align: center;
                transition: all 125ms ease-in-out;

                &:hover {
                    transform: scale(1.02);
                }
            }

            .google-login {
                background-color: #fff;
                border: 1px solid #ccc;
                color: #333;

                &:hover {
                    background-color: #f5f5f5;
                }
            }
        }

        .password-strength-meter {
            height: 4px;
            background-color: #eee;
            margin: 10px 0;
            border-radius: 2px;
            overflow: hidden;

            .password-strength-meter-fill {
                height: 100%;
                background-color: #ff0000;
                transition: width 0.3s ease-in-out;

                &[style*="width: 25%"] {
                    background-color: #ff0000;
                }

                &[style*="width: 50%"] {
                    background-color: #ffa500;
                }

                &[style*="width: 75%"] {
                    background-color: #ffff00;
                }

                &[style*="width: 100%"] {
                    background-color: #00ff00;
                }
            }
        }

        .password-strength-label {
            font-size: 12px;
            color: #666;
            margin-bottom: 10px;
        }

        .password-requirements {
            margin-top: 10px;
            font-size: 12px;

            .requirement {
                color: #999;
                margin-bottom: 5px;
                position: relative;
                padding-left: 20px;

                &:before {
                    content: "×";
                    position: absolute;
                    left: 0;
                    color: #ff0000;
                }

                &.valid {
                    color: #00a400;

                    &:before {
                        content: "✓";
                        color: #00a400;
                    }
                }
            }
        }

        .actions-toolbar {
            margin-top: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;

            .action {
                width: 100% !important;
                margin: 0 !important;
            }

            .primary {
                flex: 1;
                width: 100%;
            }

            .secondary {
                .action.back {
                    background: none;
                    border: none;
                    color: #006bb4;
                    text-decoration: underline;
                    padding: 0;

                    &:hover {
                        color: #006bb4;
                        text-decoration: none;
                    }
                }
            }
        }
    }
}

//
// Mobile
//

@media (max-width: 1024px) {
    .primary {
        margin: 0 !important;
        width: 100% !important;
    }

    .captcha-image img {
        width: 70%;
        height: auto;
    }

    .action.primary {
        height: 46px;
        margin: 0;
        margin-right: 0 !important;

        &::before {
            display: none;
        }

        span {
            width: 100%;
            text-align: center;
        }
    }

    .password-strength-meter {
        padding: 0;
    }
}