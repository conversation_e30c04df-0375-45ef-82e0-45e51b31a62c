define([
  'uiComponent',
  'ko',
  'jquery',
  'Magento_Ui/js/modal/modal',
  'sociallogin',
  'mage/validation',
  'Magento_Customer/js/customer-data',
  'underscore',
  'sidebar',
  'mage/dropdown',
  'mage/collapsible'
], function (Component, ko, $, modal, sociallogin, validation, customerData, _, sidebar, dropdown, collapsible) {
  'use strict';

  console.log('Profar_CustomRegistration: Module initialized');

  return Component.extend({
    defaults: {
      template: 'Profar_CustomRegistration/registration/form',
      isVisible: ko.observable(true),
      currentStep: ko.observable(1),
      email: ko.observable(''),
      firstname: ko.observable(''),
      lastname: ko.observable(''),
      verificationCode: ko.observable(''),
      password: ko.observable(''),
      passwordConfirmation: ko.observable(''),
      passwordStrength: ko.observable(0),
      hasMinLength: ko.observable(false),
      hasUppercase: ko.observable(false),
      hasLowercase: ko.observable(false),
      hasNumber: ko.observable(false),
      hasSpecialChar: ko.observable(false),
      socialLoginButtons: ko.observableArray([]),
      isVerifying: ko.observable(false),
      verificationError: ko.observable(''),
      passwordStrengthLabel: ko.observable(''),
      welcomeText: ko.observable('')
    },

    initialize: function () {
      console.log('Profar_CustomRegistration: Component initializing');
      this._super();
      console.log('Template path:', this.template);
      console.log('Component DOM parent:', $('[data-bind="scope: \'customRegistration\'"]').length);

      // Initialize observables
      this.isVisible = ko.observable(true);
      this.currentStep = ko.observable(1);
      this.email = ko.observable('');
      this.firstname = ko.observable('');
      this.lastname = ko.observable('');
      this.verificationCode = ko.observable('');
      this.password = ko.observable('');
      this.passwordConfirmation = ko.observable('');
      this.passwordStrength = ko.observable(0);
      this.hasMinLength = ko.observable(false);
      this.hasUppercase = ko.observable(false);
      this.hasLowercase = ko.observable(false);
      this.hasNumber = ko.observable(false);
      this.hasSpecialChar = ko.observable(false);
      this.socialLoginButtons = ko.observableArray([]);
      this.isVerifying = ko.observable(false);
      this.verificationError = ko.observable('');
      this.passwordStrengthLabel = ko.observable('');
      this.welcomeText = ko.observable('');

      // Initialize computed observables
      var self = this;
      this.isPasswordValid = ko.computed(function () {
        return self.hasMinLength() &&
          self.hasUppercase() &&
          self.hasLowercase() &&
          self.hasNumber() &&
          self.hasSpecialChar() &&
          self.password() === self.passwordConfirmation();
      });

      this.loadSocialButtons();
      this.initializeRegistration();
      this.bindRegisterLink();
      console.log('Profar_CustomRegistration: Component initialized');
      return this;
    },

    getBaseUrl: function () {
      var baseUrl = '';
      if (window.customRegistration && window.customRegistration.baseUrl) {
        baseUrl = window.customRegistration.baseUrl;
      }
      // Ensure baseUrl ends with a slash
      if (baseUrl && !baseUrl.endsWith('/')) {
        baseUrl += '/';
      }
      return baseUrl;
    },

    bindRegisterLink: function () {
      console.log('Profar_CustomRegistration: Binding register link');
      var self = this;

      // Interceptar el clic en el enlace de registro
      $(document).on('click', 'a.action-register', function (e) {
        console.log('Profar_CustomRegistration: Register link clicked');
        e.preventDefault();
        e.stopPropagation();

        // Si estamos en la página de registro, no hacemos nada
        if (window.location.pathname.indexOf('customer/account/create') !== -1) {
          return;
        }

        self.showModal();
      });
    },

    initModal: function () {
      console.log('Profar_CustomRegistration: Initializing modal');
      var self = this;
      var modalElement = $('.modal-custom-registration');

      modalElement.modal({
        type: 'popup',
        responsive: true,
        innerScroll: true,
        title: $.mage.__('Create Account'),
        buttons: [],
        closed: function () {
          self.resetForm();
        }
      });

      this.modal = modalElement.modal('instance');
      console.log('Profar_CustomRegistration: Modal initialized');
    },

    loadSocialButtons: function () {
      if (window.customRegistration && window.customRegistration.socialLoginButtons) {
        var buttons = [];
        $.each(window.customRegistration.socialLoginButtons, function (type, data) {
          buttons.push({
            type: type,
            label: type.charAt(0).toUpperCase() + type.slice(1),
            endpoint: data.endpoint
          });
        });
        this.socialLoginButtons(buttons);
      }
    },

    showModal: function () {
      this.isVisible(true);
      this.modal.openModal();
    },

    hideModal: function () {
      this.modal.closeModal();
    },

    resetForm: function () {
      this.currentStep(1);
      this.email('');
      this.firstname('');
      this.lastname('');
      this.verificationCode('');
      this.password('');
      this.passwordConfirmation('');
      this.passwordStrength(0);
    },

    socialLogin: function (socialButton) {
      if (socialButton.endpoint) {
        window.sl.socialLogin(socialButton.endpoint);
      }
    },

    showEmailForm: function () {
      console.log('Profar_CustomRegistration: Showing email form');
      this.currentStep(2);
    },

    goBack: function () {
      this.currentStep(this.currentStep() - 1);
    },

    submitVerificationCode: function () {
      var self = this;
      console.log('Profar_CustomRegistration: Submitting verification code');
      var code = this.verificationCode();

      if (!code) {
        console.log('Profar_CustomRegistration: Verification code is empty');
        return;
      }

      this.isVerifying(true);
      this.verificationError('');

      var baseUrl = this.getBaseUrl();
      var url = baseUrl + 'customregistration/account/verifycode';
      console.log('Verifying code at:', url);

      $.ajax({
        url: url,
        type: 'POST',
        data: {
          verification_code: code,
          email: this.email(),
          firstname: this.firstname(),
          lastname: this.lastname()
        },
        success: function (response) {
          console.log('Profar_CustomRegistration: Verification response', response);
          if (response.success) {
            self.currentStep(4); // Show password form
          } else {
            self.verificationError(response.message);
          }
        },
        error: function (xhr, status, error) {
          console.error('Profar_CustomRegistration: Verification error', error);
          self.verificationError('An error occurred. Please try again.');
        },
        complete: function () {
          self.isVerifying(false);
        }
      });
    },

    validateEmailForm: function () {
      var form = $('.email-form');
      return form.validation() && form.validation('isValid');
    },

    checkPasswordStrength: function () {
      var password = this.password();
      var strength = 0;

      if (password.length >= 8) {
        strength += 20;
        this.hasMinLength(true);
      } else {
        this.hasMinLength(false);
      }

      if (/[A-Z]/.test(password)) {
        strength += 20;
        this.hasUppercase(true);
      } else {
        this.hasUppercase(false);
      }

      if (/[a-z]/.test(password)) {
        strength += 20;
        this.hasLowercase(true);
      } else {
        this.hasLowercase(false);
      }

      if (/[0-9]/.test(password)) {
        strength += 20;
        this.hasNumber(true);
      } else {
        this.hasNumber(false);
      }

      if (/[^A-Za-z0-9]/.test(password)) {
        strength += 20;
        this.hasSpecialChar(true);
      } else {
        this.hasSpecialChar(false);
      }

      this.passwordStrength(strength);
    },

    loginWithGoogle: function () {
      console.log('Profar_CustomRegistration: Iniciating Google login');
      var googleEndpoint = '';
      if (window.customRegistration && window.customRegistration.socialLoginButtons
        && window.customRegistration.socialLoginButtons.google) {
        googleEndpoint = window.customRegistration.socialLoginButtons.google.endpoint;
      }
      if (googleEndpoint && googleEndpoint !== '#') {
        window.location.href = googleEndpoint;
      } else {
        alert('El inicio de sesión con Google no está disponible actualmente.');
      }
    },

    createAccount: function () {
      var self = this;
      if (this.validatePasswordForm()) {
        var baseUrl = this.getBaseUrl();
        console.log('Creando cuenta en:', baseUrl + 'customregistration/account/create');

        $.ajax({
          url: baseUrl + 'customregistration/account/create',
          type: 'POST',
          data: {
            password: this.password(),
            password_confirmation: this.passwordConfirmation()
          },
          success: function (response) {
            if (response.success) {
              // Mostrar modal de bienvenida (paso 5)
              self.welcomeText(
                $.mage.__('Your account has been created successfully!') +
                '<br><br>' +
                $.mage.__('You are now logged in as:') +
                ' <b>' + self.firstname() + ' ' + self.lastname() + '</b> (' + self.email() + ')'
              );
              self.currentStep(5);
            } else {
              alert(response.message || 'Error al crear la cuenta');
            }
          },
          error: function (xhr, status, error) {
            console.error('Error al crear cuenta:', error);
            alert('Ocurrió un error al crear la cuenta. Por favor, intente nuevamente.');
          }
        });
      }
    },

    validatePasswordForm: function () {
      var form = $('.verification-form');
      return form.validation() && form.validation('isValid');
    },

    initializeRegistration: function () {
      console.log('Profar_CustomRegistration: Initializing registration functionality');
      var self = this;

      // Initialize verification code
      this.verificationCode = ko.observable('');
      this.isVerifying = ko.observable(false);
      this.verificationError = ko.observable('');

      // Initialize password
      this.passwordStrength = ko.observable(0);
      this.passwordStrengthLabel = ko.observable('');

      // Password strength meter
      this.updatePasswordStrength = function (password) {
        console.log('Profar_CustomRegistration: Updating password strength');
        var strength = 0;
        var label = '';

        if (password.length >= 8) strength += 25;
        if (password.match(/[A-Z]/)) strength += 25;
        if (password.match(/[0-9]/)) strength += 25;
        if (password.match(/[^A-Za-z0-9]/)) strength += 25;

        if (strength <= 25) label = 'Weak';
        else if (strength <= 50) label = 'Medium';
        else if (strength <= 75) label = 'Strong';
        else label = 'Very Strong';

        self.passwordStrength(strength);
        self.passwordStrengthLabel(label);
        console.log('Profar_CustomRegistration: Password strength updated', { strength, label });
      };

      // Show email form
      this.showEmailForm = function () {
        console.log('Profar_CustomRegistration: Showing email form');
        $('#registration-options').hide();
        $('#email-registration-form').show();
      };

      // Show verification form
      this.showVerificationForm = function () {
        console.log('Profar_CustomRegistration: Showing verification form');
        $('#email-registration-form').hide();
        $('#verification-form').show();
      };

      // Show password form
      this.showPasswordForm = function () {
        console.log('Profar_CustomRegistration: Showing password form');
        $('#verification-form').hide();
        $('#password-form').show();
      };

      // Handle email submission
      this.submitEmail = function () {
        console.log('Profar_CustomRegistration: Submitting email');
        var email = $('#registration-email').val();

        if (!email) {
          console.log('Profar_CustomRegistration: Email is empty');
          return;
        }

        var baseUrl = '';
        if (window.customRegistration && window.customRegistration.baseUrl) {
          baseUrl = window.customRegistration.baseUrl;
        }

        $.ajax({
          url: baseUrl + 'customregistration/account/sendcode',
          type: 'POST',
          data: {
            email: email
          },
          success: function (response) {
            console.log('Profar_CustomRegistration: Email submission response', response);
            if (response.success) {
              self.showVerificationForm();
            } else {
              self.verificationError(response.message);
            }
          },
          error: function (xhr, status, error) {
            console.error('Profar_CustomRegistration: Email submission error', error);
            self.verificationError('An error occurred. Please try again.');
          }
        });
      };

      console.log('Profar_CustomRegistration: Registration functionality initialized');
    },

    sendVerificationCode: function () {
      var self = this;
      if (this.validateEmailForm()) {
        var baseUrl = this.getBaseUrl();
        console.log('Enviando código de verificación a:', baseUrl + 'customregistration/account/sendcode');

        $.ajax({
          url: baseUrl + 'customregistration/account/sendcode',
          type: 'POST',
          data: {
            email: this.email(),
            firstname: this.firstname(),
            lastname: this.lastname()
          },
          success: function (response) {
            if (response.success) {
              self.currentStep(3);
            } else {
              alert({
                content: response.message || 'Error al enviar el código de verificación'
              });
            }
          },
          error: function (xhr, status, error) {
            console.error('Error al enviar código:', error);
            alert({
              content: 'Ocurrió un error al enviar el código. Por favor, intente nuevamente.'
            });
          }
        });
      }
    },

    goToAccount: function () {
      window.location.href = '/customer/account';
    }
  });
}); 