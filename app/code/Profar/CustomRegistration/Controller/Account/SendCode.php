<?php
namespace Profar\CustomRegistration\Controller\Account;

use Magento\Framework\App\Action\Action;
use Magento\Framework\App\Action\Context;
use Magento\Framework\Controller\Result\JsonFactory;
use Magento\Framework\Math\Random;
use Magento\Framework\Mail\Template\TransportBuilder;
use Magento\Store\Model\StoreManagerInterface;
use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Store\Model\ScopeInterface;
use Magento\Customer\Model\Session;
use Psr\Log\LoggerInterface;

class SendCode extends Action
{
    protected $resultJsonFactory;
    protected $random;
    protected $transportBuilder;
    protected $storeManager;
    protected $scopeConfig;
    protected $customerSession;
    protected $logger;

    public function __construct(
        Context $context,
        JsonFactory $resultJsonFactory,
        Random $random,
        TransportBuilder $transportBuilder,
        StoreManagerInterface $storeManager,
        ScopeConfigInterface $scopeConfig,
        Session $customerSession,
        LoggerInterface $logger
    ) {
        parent::__construct($context);
        $this->resultJsonFactory = $resultJsonFactory;
        $this->random = $random;
        $this->transportBuilder = $transportBuilder;
        $this->storeManager = $storeManager;
        $this->scopeConfig = $scopeConfig;
        $this->customerSession = $customerSession;
        $this->logger = $logger;
    }

    public function execute()
    {
        $this->logger->info('Profar_CustomRegistration: Starting code generation');
        $result = $this->resultJsonFactory->create();

        if (!$this->getRequest()->isPost()) {
            $this->logger->warning('Profar_CustomRegistration: Non-POST request received');
            return $result->setData([
                'success' => false,
                'message' => __('Invalid request method.')
            ]);
        }

        try {
            $email = $this->getRequest()->getParam('email');
            $firstname = $this->getRequest()->getParam('firstname');
            $lastname = $this->getRequest()->getParam('lastname');

            if (!$email || !$firstname || !$lastname) {
                $this->logger->warning('Profar_CustomRegistration: Missing required parameters');
                return $result->setData([
                    'success' => false,
                    'message' => __('Missing required parameters.')
                ]);
            }

            // Generate verification code
            $code = $this->random->getRandomString(6, '0123456789');

            // Store in session
            $this->customerSession->setVerificationCode($code);
            $this->customerSession->setRegistrationEmail($email);
            $this->customerSession->setRegistrationFirstname($firstname);
            $this->customerSession->setRegistrationLastname($lastname);

            // Get email configuration
            $store = $this->storeManager->getStore();
            $fromEmail = $this->scopeConfig->getValue(
                'trans_email/ident_general/email',
                ScopeInterface::SCOPE_STORE
            );
            $fromName = $this->scopeConfig->getValue(
                'trans_email/ident_general/name',
                ScopeInterface::SCOPE_STORE
            );

            $this->logger->info('Profar_CustomRegistration: Sending verification email', [
                'email' => $email,
                'firstname' => $firstname,
                'lastname' => $lastname
            ]);

            // Send email
            $transport = $this->transportBuilder
                ->setTemplateIdentifier('custom_registration_verification_email')
                ->setTemplateOptions([
                    'area' => 'frontend',
                    'store' => $store->getId()
                ])
                ->setTemplateVars([
                    'code' => $code,
                    'firstname' => $firstname,
                    'lastname' => $lastname
                ])
                ->setFrom([
                    'email' => $fromEmail,
                    'name' => $fromName
                ])
                ->addTo($email, $firstname . ' ' . $lastname)
                ->getTransport();

            $transport->sendMessage();

            $this->logger->info('Profar_CustomRegistration: Verification code sent successfully');
            $this->logger->info('Profar_CustomRegistration: CODE '. $code);

            return $result->setData([
                'success' => true,
                'message' => __('Verification code has been sent to your email.')
            ]);
        } catch (\Exception $e) {
            $this->logger->error('Profar_CustomRegistration: ' . $e->getMessage());
            return $result->setData([
                'success' => false,
                'message' => __('An error occurred while sending the verification code.')
            ]);
        }
    }
}
