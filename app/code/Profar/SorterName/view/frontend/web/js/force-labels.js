require(['jquery'], function ($) {
    $(document).ready(function () {
        const orderParam = new URLSearchParams(window.location.search).get('product_list_order');

        const mapLabels = {
            'name': 'Nombre, ascendente',
            'name_desc': 'Nombre, descendente',
            'price': 'Precio: menor a mayor',
            'price_desc': 'Precio: mayor a menor'
        };

        const $select = $('select.sorter-options');

        if ($select.length && orderParam) {
            $select.find('option').each(function () {
                const val = $(this).val();

                if (mapLabels[val]) {
                    $(this).text(mapLabels[val]);
                } 

                if (val === orderParam) {
                    $(this).prop('selected', true);
                } else {
                    $(this).prop('selected', false);
                }
            });
        }
    });
});
