<?php
declare(strict_types=1);

namespace Profar\SuggestionsBook\Model\ResourceModel\ProfarSuggestionsBook;

use Magento\Framework\Model\ResourceModel\Db\Collection\AbstractCollection;

class Collection extends AbstractCollection
{

    /**
     * @inheritDoc
     */
    protected $_idFieldName = 'entity_id';

    /**
     * @inheritDoc
     */
    protected function _construct()
    {
        $this->_init(
            \Profar\SuggestionsBook\Model\ProfarSuggestionsBook::class,
            \Profar\SuggestionsBook\Model\ResourceModel\ProfarSuggestionsBook::class
        );
    }
}

