<?php
namespace Profar\CheckoutFields\Model\Attribute\Source;

use Magento\Eav\Model\Entity\Attribute\Source\AbstractSource;

class DocumentType extends AbstractSource
{
    /**
     * Get all options
     *
     * @return array
     */
    public function getAllOptions()
    {
        if (!$this->_options) {
            $this->_options = [
                ['value' => '', 'label' => __('Please select')],
                ['value' => 'rut', 'label' => __('RUT')],
            ];
        }
        return $this->_options;
    }

    /**
     * Get options in "key-value" format
     *
     * @return array
     */
    public function toArray()
    {
        $array = [];
        foreach ($this->getAllOptions() as $option) {
            $array[$option['value']] = $option['label'];
        }
        return $array;
    }
}
