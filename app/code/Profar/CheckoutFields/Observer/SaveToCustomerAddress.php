<?php

namespace Profar\CheckoutFields\Observer;

use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;
use Magento\Customer\Api\AddressRepositoryInterface;
use Magento\Customer\Api\Data\AddressInterfaceFactory;
use Magento\Framework\Exception\LocalizedException;
use Psr\Log\LoggerInterface;

class SaveToCustomerAddress implements ObserverInterface
{
    protected $addressRepository;
    protected $addressFactory;
    protected $logger;

    public function __construct(
        AddressRepositoryInterface $addressRepository,
        AddressInterfaceFactory $addressFactory,
        LoggerInterface $logger
    ) {
        $this->addressRepository = $addressRepository;
        $this->addressFactory = $addressFactory;
        $this->logger = $logger;
    }

    public function execute(Observer $observer)
    {
        try {
            $quote = $observer->getEvent()->getQuote();
            $order = $observer->getEvent()->getOrder();

            if (!$quote || !$order) {
                return;
            }

            // Save document fields from quote addresses to customer addresses
            $this->saveAddressDocumentFields($quote->getShippingAddress(), $order->getCustomerId());
            $this->saveAddressDocumentFields($quote->getBillingAddress(), $order->getCustomerId());

            // Also save to order
            $order->setData('document_type', $quote->getData('document_type'));
            $order->setData('document_number', $quote->getData('document_number'));

        } catch (\Exception $e) {
            $this->logger->error('Error saving document fields to customer address: ' . $e->getMessage(), [
                'exception' => $e
            ]);
        }
    }

    /**
     * Save document fields from quote address to customer address
     *
     * @param \Magento\Quote\Model\Quote\Address $quoteAddress
     * @param int $customerId
     */
    private function saveAddressDocumentFields($quoteAddress, $customerId)
    {
        if (!$quoteAddress || !$customerId || !$quoteAddress->getCustomerAddressId()) {
            return;
        }

        try {
            $customerAddress = $this->addressRepository->getById($quoteAddress->getCustomerAddressId());
            
            // Get document fields from quote address custom attributes
            $documentType = $this->getCustomAttributeValue($quoteAddress, 'document_type');
            $documentNumber = $this->getCustomAttributeValue($quoteAddress, 'document_number');

            if ($documentType || $documentNumber) {
                if ($documentType) {
                    $customerAddress->setCustomAttribute('document_type', $documentType);
                }
                if ($documentNumber) {
                    $customerAddress->setCustomAttribute('document_number', $documentNumber);
                }
                
                $this->addressRepository->save($customerAddress);
            }

        } catch (\Exception $e) {
            $this->logger->error('Error updating customer address with document fields: ' . $e->getMessage(), [
                'customer_address_id' => $quoteAddress->getCustomerAddressId(),
                'exception' => $e
            ]);
        }
    }

    /**
     * Get custom attribute value from quote address
     *
     * @param \Magento\Quote\Model\Quote\Address $address
     * @param string $attributeCode
     * @return string|null
     */
    private function getCustomAttributeValue($address, $attributeCode)
    {
        $customAttributes = $address->getCustomAttributes();
        if ($customAttributes && isset($customAttributes[$attributeCode])) {
            return $customAttributes[$attributeCode]->getValue();
        }
        return null;
    }
}
