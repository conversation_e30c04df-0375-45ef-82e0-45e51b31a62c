<?php

namespace Profar\CheckoutFields\Observer;

use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;
use Magento\Framework\App\RequestInterface;
use Psr\Log\LoggerInterface;

class CustomerAddressSave implements ObserverInterface
{
    protected $request;
    protected $logger;

    public function __construct(
        RequestInterface $request,
        LoggerInterface $logger
    ) {
        $this->request = $request;
        $this->logger = $logger;
    }

    /**
     * Save custom attributes when customer address is saved
     *
     * @param Observer $observer
     * @return void
     */
    public function execute(Observer $observer)
    {
        try {
            $customerAddress = $observer->getEvent()->getCustomerAddress();
            $customAttributes = $this->request->getParam('custom_attributes', []);
            
            if ($customerAddress && !empty($customAttributes)) {
                if (isset($customAttributes['document_type'])) {
                    $customerAddress->setCustomAttribute('document_type', $customAttributes['document_type']);
                }
                
                if (isset($customAttributes['document_number'])) {
                    $customerAddress->setCustomAttribute('document_number', $customAttributes['document_number']);
                }
            }
        } catch (\Exception $e) {
            $this->logger->error('Error in CustomerAddressSave observer: ' . $e->getMessage(), [
                'exception' => $e
            ]);
        }
    }
}
