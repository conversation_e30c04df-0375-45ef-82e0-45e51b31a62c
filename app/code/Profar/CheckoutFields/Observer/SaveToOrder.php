<?php

namespace Profar\CheckoutFields\Observer;

use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;

class SaveToOrder implements ObserverInterface
{
    public function execute(Observer $observer)
    {
        $quote = $observer->getEvent()->getQuote();
        $order = $observer->getEvent()->getOrder();

        // Try to get document fields from quote first (legacy support)
        $documentType = $quote->getData('document_type');
        $documentNumber = $quote->getData('document_number');

        // If not found in quote, try to get from shipping address custom attributes
        if (!$documentType || !$documentNumber) {
            $shippingAddress = $quote->getShippingAddress();
            if ($shippingAddress) {
                $documentType = $documentType ?: $this->getCustomAttributeValue($shippingAddress, 'document_type');
                $documentNumber = $documentNumber ?: $this->getCustomAttributeValue($shippingAddress, 'document_number');
            }
        }

        // If still not found, try billing address
        if (!$documentType || !$documentNumber) {
            $billingAddress = $quote->getBillingAddress();
            if ($billingAddress) {
                $documentType = $documentType ?: $this->getCustomAttributeValue($billingAddress, 'document_type');
                $documentNumber = $documentNumber ?: $this->getCustomAttributeValue($billingAddress, 'document_number');
            }
        }

        $order->setData('document_type', $documentType);
        $order->setData('document_number', $documentNumber);
    }

    /**
     * Get custom attribute value from address
     *
     * @param \Magento\Quote\Model\Quote\Address $address
     * @param string $attributeCode
     * @return string|null
     */
    private function getCustomAttributeValue($address, $attributeCode)
    {
        $customAttributes = $address->getCustomAttributes();
        if ($customAttributes && isset($customAttributes[$attributeCode])) {
            return $customAttributes[$attributeCode]->getValue();
        }
        return null;
    }
}
