<?php

namespace Profar\CheckoutFields\Plugin\Customer\Address;

use <PERSON><PERSON><PERSON>\Customer\Controller\Adminhtml\Address\Save as AddressSave;
use Magento\Framework\App\RequestInterface;
use Magento\Customer\Api\AddressRepositoryInterface;
use Magento\Customer\Api\Data\AddressInterfaceFactory;

class Save
{
    protected $request;
    protected $addressRepository;
    protected $addressFactory;

    public function __construct(
        RequestInterface $request,
        AddressRepositoryInterface $addressRepository,
        AddressInterfaceFactory $addressFactory
    ) {
        $this->request = $request;
        $this->addressRepository = $addressRepository;
        $this->addressFactory = $addressFactory;
    }

    /**
     * Save custom attributes when saving customer address in admin
     *
     * @param AddressSave $subject
     * @param \Closure $proceed
     * @return mixed
     */
    public function aroundExecute(AddressSave $subject, \Closure $proceed)
    {
        $result = $proceed();

        try {
            $addressId = $this->request->getParam('id');
            $documentType = $this->request->getParam('document_type');
            $documentNumber = $this->request->getParam('document_number');

            if ($addressId && ($documentType || $documentNumber)) {
                $address = $this->addressRepository->getById($addressId);
                
                if ($documentType) {
                    $address->setCustomAttribute('document_type', $documentType);
                }
                if ($documentNumber) {
                    $address->setCustomAttribute('document_number', $documentNumber);
                }
                
                $this->addressRepository->save($address);
            }
        } catch (\Exception $e) {
            // Log error but don't break the save process
            error_log('Error saving document fields to customer address: ' . $e->getMessage());
        }

        return $result;
    }
}
