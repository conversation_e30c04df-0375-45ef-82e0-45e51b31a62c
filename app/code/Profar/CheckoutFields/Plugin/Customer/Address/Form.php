<?php

namespace Profar\CheckoutFields\Plugin\Customer\Address;

use Magento\Customer\Block\Address\Edit;

class Form
{
    /**
     * Add custom attributes to customer address form
     *
     * @param Edit $subject
     * @param string $result
     * @return string
     */
    public function afterGetNameInLayout(Edit $subject, $result)
    {
        // This plugin ensures that our custom attributes are loaded in the address form
        return $result;
    }
}
