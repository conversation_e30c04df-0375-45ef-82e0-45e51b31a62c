<?php

namespace Profar\CheckoutFields\Plugin\Customer\Address;

use Magento\Customer\Controller\Address\FormPost;
use Magento\Framework\App\RequestInterface;
use Magento\Customer\Api\AddressRepositoryInterface;
use Magento\Framework\Exception\LocalizedException;
use Psr\Log\LoggerInterface;

class SaveFrontend
{
    protected $request;
    protected $addressRepository;
    protected $logger;

    public function __construct(
        RequestInterface $request,
        AddressRepositoryInterface $addressRepository,
        LoggerInterface $logger
    ) {
        $this->request = $request;
        $this->addressRepository = $addressRepository;
        $this->logger = $logger;
    }

    /**
     * Save custom attributes when saving customer address in frontend
     *
     * @param FormPost $subject
     * @param mixed $result
     * @return mixed
     */
    public function afterExecute(FormPost $subject, $result)
    {
        try {
            $customAttributes = $this->request->getParam('custom_attributes', []);

            if (!empty($customAttributes)) {
                $addressId = $this->request->getParam('id');

                // If no address ID, try to get the newly created address ID from session
                if (!$addressId) {
                    // For new addresses, we need to find the most recently created address
                    // This is a bit tricky, but we can use the customer session
                    $objectManager = \Magento\Framework\App\ObjectManager::getInstance();
                    $customerSession = $objectManager->get(\Magento\Customer\Model\Session::class);
                    $customer = $customerSession->getCustomer();

                    if ($customer && $customer->getId()) {
                        $addresses = $customer->getAddresses();
                        if (!empty($addresses)) {
                            // Get the last address (most recently created)
                            $lastAddress = end($addresses);
                            $addressId = $lastAddress->getId();
                        }
                    }
                }

                if ($addressId) {
                    $address = $this->addressRepository->getById($addressId);

                    if (isset($customAttributes['document_type'])) {
                        $address->setCustomAttribute('document_type', $customAttributes['document_type']);
                    }

                    if (isset($customAttributes['document_number'])) {
                        $address->setCustomAttribute('document_number', $customAttributes['document_number']);
                    }

                    $this->addressRepository->save($address);
                }
            }
        } catch (\Exception $e) {
            $this->logger->error('Error saving document fields to customer address: ' . $e->getMessage(), [
                'exception' => $e
            ]);
        }

        return $result;
    }
}
