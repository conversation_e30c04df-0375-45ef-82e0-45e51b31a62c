<?php

namespace Profar\CheckoutFields\Plugin\Checkout;

class LayoutProcessorPlugin
{
    public function afterProcess(
        \Magento\Checkout\Block\Checkout\LayoutProcessor $subject,
        array $jsLayout
    ) {
        // Add fields to shipping address
        $this->addCustomFieldsToAddress($jsLayout, 'shipping');

        // Add fields to billing address
        $this->addCustomFieldsToAddress($jsLayout, 'billing');

        return $jsLayout;
    }

    /**
     * Add custom fields to shipping or billing address
     *
     * @param array $jsLayout
     * @param string $addressType
     */
    private function addCustomFieldsToAddress(array &$jsLayout, string $addressType)
    {
        $customFields = $this->getCustomFields($addressType);

        if ($addressType === 'shipping') {
            $fieldsetPath = 'components.checkout.children.steps.children.shipping-step.children.shippingAddress.children.shipping-address-fieldset.children';
        } else {
            $fieldsetPath = 'components.checkout.children.steps.children.billing-step.children.payment.children.payments-list.children';
        }

        foreach ($customFields as $fieldCode => $fieldConfig) {
            if ($addressType === 'shipping') {
                $jsLayout['components']['checkout']['children']['steps']['children']['shipping-step']['children']['shippingAddress']['children']['shipping-address-fieldset']['children'][$fieldCode] = $fieldConfig;
            } else {
                // For billing address, we need to add to each payment method
                if (isset($jsLayout['components']['checkout']['children']['steps']['children']['billing-step']['children']['payment']['children']['payments-list']['children'])) {
                    foreach ($jsLayout['components']['checkout']['children']['steps']['children']['billing-step']['children']['payment']['children']['payments-list']['children'] as $paymentCode => &$paymentComponent) {
                        if (isset($paymentComponent['children']['form-fields']['children'])) {
                            $paymentComponent['children']['form-fields']['children'][$fieldCode] = $fieldConfig;
                        }
                    }
                }

                // Also add to the default billing address form
                if (isset($jsLayout['components']['checkout']['children']['steps']['children']['billing-step']['children']['payment']['children']['afterMethods']['children']['billing-address-form'])) {
                    $jsLayout['components']['checkout']['children']['steps']['children']['billing-step']['children']['payment']['children']['afterMethods']['children']['billing-address-form']['children']['form-fields']['children'][$fieldCode] = $fieldConfig;
                }
            }
        }
    }

    /**
     * Get custom fields configuration
     *
     * @param string $addressType
     * @return array
     */
    private function getCustomFields(string $addressType): array
    {
        $scope = $addressType === 'shipping' ? 'shippingAddress' : 'billingAddress';

        return [
            'document_type' => [
                'component' => 'Magento_Ui/js/form/element/select',
                'config' => [
                    'customScope' => $scope,
                    'template' => 'ui/form/field',
                    'elementTmpl' => 'ui/form/element/select',
                    'id' => 'document_type_' . $addressType,
                    'options' => [
                        ['value' => '', 'label' => __('Seleccione un tipo')],
                        ['value' => 'rut', 'label' => __('RUT')],
                    ]
                ],
                'dataScope' => $scope . '.custom_attributes.document_type',
                'label' => __('Tipo de documento'),
                'provider' => 'checkoutProvider',
                'visible' => true,
                'validation' => ['required-entry' => true],
                'sortOrder' => 50
            ],
            'document_number' => [
                'component' => 'Magento_Ui/js/form/element/abstract',
                'config' => [
                    'customScope' => $scope,
                    'template' => 'ui/form/field',
                    'elementTmpl' => 'ui/form/element/input',
                    'id' => 'document_number_' . $addressType,
                    'dataType' => 'text',
                    'maxlength' => 11,
                    'formElement' => 'input'
                ],
                'dataScope' => $scope . '.custom_attributes.document_number',
                'label' => __('Número de documento'),
                'provider' => 'checkoutProvider',
                'visible' => true,
                'validation' => [
                    'required-entry' => true,
                    'validate-document-length' => true
                ],
                'sortOrder' => 51
            ]
        ];
    }
}
