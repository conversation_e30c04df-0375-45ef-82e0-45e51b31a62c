<?php

namespace Profar\CheckoutFields\Controller\Quote;

use Magento\Checkout\Model\Session;
use Magento\Framework\App\Action\Action;
use Magento\Framework\App\Action\Context;
use Magento\Quote\Model\QuoteIdMaskFactory;
use Magento\Quote\Api\CartRepositoryInterface;
use Magento\Framework\Controller\Result\JsonFactory;
use Magento\Framework\Exception\NoSuchEntityException;

class Save extends Action
{
    protected $quoteIdMaskFactory;
    protected $quoteRepository;
    protected $resultJsonFactory;
    protected $checkoutSession;

    public function __construct(
        Context $context,
        QuoteIdMaskFactory $quoteIdMaskFactory,
        CartRepositoryInterface $quoteRepository,
        JsonFactory $resultJsonFactory,
        Session $checkoutSession
    ) {
        parent::__construct($context);
        $this->quoteIdMaskFactory = $quoteIdMaskFactory;
        $this->quoteRepository = $quoteRepository;
        $this->resultJsonFactory = $resultJsonFactory;
        $this->checkoutSession   = $checkoutSession;
    }

    public function execute()
    {
        $result = $this->resultJsonFactory->create();
        $post = $this->getRequest()->getPostValue();

        if ($post) {

            $documentType = $post['document_type'] ?? null;
            $documentNumber = $post['document_number'] ?? null;

            try {
                if(!empty($documentType) && !empty($documentNumber)) {
                    $cartId = $this->checkoutSession->getQuote()->getId();
                    $quote = $this->quoteRepository->getActive($cartId);
                    if (!$quote->getItemsCount()) {
                        throw new NoSuchEntityException(__('Cart %1 doesn\'t contain products', $cartId));
                    }

                    // Save to quote (legacy support)
                    $quote->setData('document_type', $documentType);
                    $quote->setData('document_number', $documentNumber);

                    // Save to shipping address
                    $shippingAddress = $quote->getShippingAddress();
                    if ($shippingAddress) {
                        $shippingAddress->setData('document_type', $documentType);
                        $shippingAddress->setData('document_number', $documentNumber);
                    }

                    // Save to billing address
                    $billingAddress = $quote->getBillingAddress();
                    if ($billingAddress) {
                        $billingAddress->setData('document_type', $documentType);
                        $billingAddress->setData('document_number', $documentNumber);
                    }

                    $this->quoteRepository->save($quote);
                    return $result->setData(['success' => true]);
                }

            } catch (\Exception $e) {
                return $result->setData(['success' => false, 'error' => $e->getMessage()]);
            }
        }

        return $result->setData(['success' => false, 'error' => 'No data posted']);
    }
}
