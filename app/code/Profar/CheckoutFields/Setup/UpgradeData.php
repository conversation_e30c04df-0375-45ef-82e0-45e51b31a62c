<?php
namespace Profar\CheckoutFields\Setup;

use Magento\Framework\Setup\UpgradeDataInterface;
use Magento\Framework\Setup\ModuleContextInterface;
use Magento\Framework\Setup\ModuleDataSetupInterface;
use Magento\Customer\Setup\CustomerSetupFactory;

class UpgradeData implements UpgradeDataInterface
{
    private $customerSetupFactory;

    public function __construct(
        CustomerSetupFactory $customerSetupFactory
    ) {
        $this->customerSetupFactory = $customerSetupFactory;
    }

    public function upgrade(ModuleDataSetupInterface $setup, ModuleContextInterface $context)
    {
        $setup->startSetup();

        if (version_compare($context->getVersion(), '1.1.0', '<')) {
            $this->addCustomerAddressAttributes($setup);
        }

        $setup->endSetup();
    }

    /**
     * Add customer address attributes
     *
     * @param ModuleDataSetupInterface $setup
     */
    private function addCustomerAddressAttributes(ModuleDataSetupInterface $setup)
    {
        $customerSetup = $this->customerSetupFactory->create(['setup' => $setup]);

        // Check if attributes don't already exist before adding them
        if (!$customerSetup->getAttributeId('customer_address', 'document_type')) {
            $customerSetup->addAttribute('customer_address', 'document_type', [
                'type' => 'varchar',
                'label' => 'Document Type',
                'input' => 'select',
                'source' => 'Profar\CheckoutFields\Model\Attribute\Source\DocumentType',
                'required' => false,
                'visible' => true,
                'user_defined' => true,
                'sort_order' => 200,
                'position' => 200,
                'system' => false,
                'is_used_in_grid' => false,
                'is_visible_in_grid' => false,
                'is_filterable_in_grid' => false,
                'is_searchable_in_grid' => false,
                'used_in_forms' => [
                    'adminhtml_customer_address',
                    'customer_address_edit',
                    'customer_register_address'
                ],
            ]);
        }

        if (!$customerSetup->getAttributeId('customer_address', 'document_number')) {
            $customerSetup->addAttribute('customer_address', 'document_number', [
                'type' => 'varchar',
                'label' => 'Document Number',
                'input' => 'text',
                'required' => false,
                'visible' => true,
                'user_defined' => true,
                'sort_order' => 201,
                'position' => 201,
                'system' => false,
                'is_used_in_grid' => false,
                'is_visible_in_grid' => false,
                'is_filterable_in_grid' => false,
                'is_searchable_in_grid' => false,
                'validate_rules' => json_encode(['max_text_length' => 11]),
                'used_in_forms' => [
                    'adminhtml_customer_address',
                    'customer_address_edit',
                    'customer_register_address'
                ],
            ]);
        }
    }
}
