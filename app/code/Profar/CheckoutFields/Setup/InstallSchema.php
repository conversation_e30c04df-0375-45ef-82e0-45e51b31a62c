<?php
namespace Profar\CheckoutFields\Setup;

use Magento\Framework\Setup\InstallSchemaInterface;
use Magento\Framework\Setup\ModuleContextInterface;
use Magento\Framework\Setup\SchemaSetupInterface;
use Magento\Framework\DB\Ddl\Table;

class InstallSchema implements InstallSchemaInterface
{
    public function install(SchemaSetupInterface $setup, ModuleContextInterface $context)
    {
        $installer = $setup;
        $installer->startSetup();

        $tables = ['quote', 'quote_address', 'sales_order', 'sales_order_grid', 'customer_address_entity'];

        foreach ($tables as $table) {
            $installer->getConnection()->addColumn(
                $installer->getTable($table),
                'document_type',
                [
                    'type' => Table::TYPE_TEXT,
                    'length' => 50,
                    'nullable' => true,
                    'comment' => 'Document Type',
                ]
            );

            $installer->getConnection()->addColumn(
                $installer->getTable($table),
                'document_number',
                [
                    'type' => Table::TYPE_TEXT,
                    'length' => 50,
                    'nullable' => true,
                    'comment' => 'Document Number (RUT)',
                ]
            );
        }

        $installer->endSetup();
    }
}
