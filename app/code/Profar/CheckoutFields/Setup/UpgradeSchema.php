<?php
namespace Profar\CheckoutFields\Setup;

use Magento\Framework\Setup\UpgradeSchemaInterface;
use Magento\Framework\Setup\ModuleContextInterface;
use Magento\Framework\Setup\SchemaSetupInterface;
use Magento\Framework\DB\Ddl\Table;

class UpgradeSchema implements UpgradeSchemaInterface
{
    public function upgrade(SchemaSetupInterface $setup, ModuleContextInterface $context)
    {
        $setup->startSetup();

        if (version_compare($context->getVersion(), '1.1.0', '<')) {
            $this->addDocumentFieldsToAddressTables($setup);
        }

        $setup->endSetup();
    }

    /**
     * Add document fields to address tables
     *
     * @param SchemaSetupInterface $setup
     */
    private function addDocumentFieldsToAddressTables(SchemaSetupInterface $setup)
    {
        $connection = $setup->getConnection();
        $tables = ['quote_address', 'customer_address_entity'];

        foreach ($tables as $tableName) {
            $table = $setup->getTable($tableName);
            
            // Check if columns don't already exist before adding them
            if (!$connection->tableColumnExists($table, 'document_type')) {
                $connection->addColumn(
                    $table,
                    'document_type',
                    [
                        'type' => Table::TYPE_TEXT,
                        'length' => 50,
                        'nullable' => true,
                        'comment' => 'Document Type',
                    ]
                );
            }

            if (!$connection->tableColumnExists($table, 'document_number')) {
                $connection->addColumn(
                    $table,
                    'document_number',
                    [
                        'type' => Table::TYPE_TEXT,
                        'length' => 50,
                        'nullable' => true,
                        'comment' => 'Document Number (RUT)',
                    ]
                );
            }
        }
    }
}
