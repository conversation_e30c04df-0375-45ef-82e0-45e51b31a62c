<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:Api/etc/extension_attributes.xsd">
    <extension_attributes for="Magento\Sales\Api\Data\OrderInterface">
        <attribute code="document_type" type="string"/>
        <attribute code="document_number" type="string"/>
        <attribute code="recipe_urls" type="string"/>
    </extension_attributes>

    <extension_attributes for="Magento\Quote\Api\Data\AddressInterface">
        <attribute code="document_type" type="string"/>
        <attribute code="document_number" type="string"/>
    </extension_attributes>

    <extension_attributes for="Magento\Customer\Api\Data\AddressInterface">
        <attribute code="document_type" type="string"/>
        <attribute code="document_number" type="string"/>
    </extension_attributes>
</config>
