<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:noNamespaceSchemaLocation="urn:magento:framework:Event/etc/events.xsd">
    <event name="sales_model_service_quote_submit_before">
        <observer name="save_document_fields_to_order"
            instance="Profar\CheckoutFields\Observer\SaveToOrder" />
        <observer name="save_document_fields_to_customer_address"
            instance="Profar\CheckoutFields\Observer\SaveToCustomerAddress" />
    </event>
    <event name="sales_model_service_quote_submit_success">
        <observer name="save_document_fields_to_order_grid"
            instance="Profar\CheckoutFields\Observer\SaveToOrderGrid" />
    </event>
</config>
