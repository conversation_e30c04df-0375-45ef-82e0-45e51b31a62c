<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:DataObject/etc/fieldset.xsd">
    <scope id="global">
        <!-- Convert customer address to quote address -->
        <fieldset id="customer_address_convert_quote_address">
            <field name="document_type">
                <aspect name="to_quote_address" />
            </field>
            <field name="document_number">
                <aspect name="to_quote_address" />
            </field>
        </fieldset>
        
        <!-- Convert quote address to customer address -->
        <fieldset id="quote_address_convert_customer_address">
            <field name="document_type">
                <aspect name="to_customer_address" />
            </field>
            <field name="document_number">
                <aspect name="to_customer_address" />
            </field>
        </fieldset>
        
        <!-- Convert quote address to order address -->
        <fieldset id="quote_address_convert_order_address">
            <field name="document_type">
                <aspect name="to_order_address" />
            </field>
            <field name="document_number">
                <aspect name="to_order_address" />
            </field>
        </fieldset>
        
        <!-- Convert order address to quote address -->
        <fieldset id="order_address_convert_quote_address">
            <field name="document_type">
                <aspect name="to_quote_address" />
            </field>
            <field name="document_number">
                <aspect name="to_quote_address" />
            </field>
        </fieldset>
    </scope>
</config>
