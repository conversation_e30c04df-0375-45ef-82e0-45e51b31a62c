<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">

    <!-- Plugin for customer address form in admin -->
    <type name="Magento\Customer\Block\Address\Edit">
        <plugin name="add_document_fields_to_admin_address_form"
                type="Profar\CheckoutFields\Plugin\Customer\Address\Form"
                sortOrder="10"/>
    </type>

    <!-- Plugin for saving customer address in admin -->
    <type name="Magento\Customer\Controller\Adminhtml\Address\Save">
        <plugin name="save_document_fields_to_admin_address"
                type="Profar\CheckoutFields\Plugin\Customer\Address\Save"
                sortOrder="10"/>
    </type>
</config>
