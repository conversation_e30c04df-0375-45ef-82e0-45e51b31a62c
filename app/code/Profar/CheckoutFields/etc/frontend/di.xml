<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="Magento\Checkout\Block\Checkout\LayoutProcessor">
        <plugin name="add_custom_checkout_fields"
                type="Profar\CheckoutFields\Plugin\Checkout\LayoutProcessorPlugin"
                sortOrder="10"/>
    </type>

    <!-- Plugin for saving customer address in frontend -->
    <type name="Magento\Customer\Controller\Address\FormPost">
        <plugin name="save_document_fields_to_frontend_address"
                type="Profar\CheckoutFields\Plugin\Customer\Address\SaveFrontend"
                sortOrder="10"/>
    </type>
</config>
