<?php
/**
 * @var \Profar\CheckoutFields\Block\Customer\Address\Edit $block
 */
?>

<div class="field document_type">
    <label class="label" for="document_type">
        <span><?= $block->escapeHtml(__('Tipo de documento')) ?></span>
    </label>
    <div class="control">
        <select name="custom_attributes[document_type]" 
                id="document_type" 
                class="select"
                data-validate="{'required-entry': false}">
            <?php foreach ($block->getDocumentTypeOptions() as $value => $label): ?>
                <option value="<?= $block->escapeHtmlAttr($value) ?>" 
                        <?= $block->getDocumentType() == $value ? 'selected="selected"' : '' ?>>
                    <?= $block->escapeHtml($label) ?>
                </option>
            <?php endforeach; ?>
        </select>
    </div>
</div>

<div class="field document_number">
    <label class="label" for="document_number">
        <span><?= $block->escapeHtml(__('Número de documento')) ?></span>
    </label>
    <div class="control">
        <input type="text" 
               name="custom_attributes[document_number]" 
               id="document_number" 
               value="<?= $block->escapeHtmlAttr($block->getDocumentNumber()) ?>"
               class="input-text"
               maxlength="11"
               data-validate="{'max-text-length': 11}" />
    </div>
</div>

<script type="text/x-magento-init">
{
    "#document_number": {
        "Profar_CheckoutFields/js/customer/address-validation": {}
    }
}
</script>
