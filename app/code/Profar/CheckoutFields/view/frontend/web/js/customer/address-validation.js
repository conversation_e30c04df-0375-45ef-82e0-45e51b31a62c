define([
    'jquery',
    'mage/translate'
], function ($, $t) {
    'use strict';

    return function (config, element) {
        $(element).on('input', function () {
            var value = $(this).val();
            if (value.length > 11) {
                $(this).val(value.substring(0, 11));
            }
        });

        // Add validation message
        $(element).on('blur', function () {
            var value = $(this).val();
            var parent = $(this).closest('.field');
            var errorMsg = parent.find('.field-error');
            
            if (errorMsg.length) {
                errorMsg.remove();
            }
            
            if (value.length > 11) {
                var error = $('<div class="field-error">' + 
                    $t('El número de documento no puede tener más de 11 caracteres.') + 
                    '</div>');
                parent.append(error);
            }
        });
    };
});
