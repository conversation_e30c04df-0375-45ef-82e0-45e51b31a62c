define([
    'jquery',
    'mage/utils/wrapper',
    'Magento_Checkout/js/model/quote',
    'Magento_Customer/js/model/customer',
    'mage/url',
    'Magento_Checkout/js/model/error-processor'
], function (
    $,
    wrapper,
    quote,
    customer,
    urlBuilder,
    errorProcessor
) {
    'use strict';

    return function (placeOrderAction) {
        return wrapper.wrap(placeOrderAction, function (originalAction, paymentData, messageContainer) {
            var shippingAddress = quote.shippingAddress();
            var billingAddress = quote.billingAddress();
            var isCustomer = customer.isLoggedIn();
            var quoteId = quote.getQuoteId();
            var documentType = "";
            var documentNumber = "";

            // Try to get from shipping address first
            if (shippingAddress && shippingAddress.customAttributes) {
                documentType = shippingAddress.customAttributes.document_type || "";
                documentNumber = shippingAddress.customAttributes.document_number || "";
            }

            // If not found in shipping, try billing address
            if ((!documentType || !documentNumber) && billingAddress && billingAddress.customAttributes) {
                documentType = documentType || billingAddress.customAttributes.document_type || "";
                documentNumber = documentNumber || billingAddress.customAttributes.document_number || "";
            }

            // If still not found, try to get from form fields as a fallback
            if (!documentType) {
                documentType = $('[name="shippingAddress.custom_attributes.document_type"]').val() ||
                              $('[name="billingAddress.custom_attributes.document_type"]').val() ||
                              $('[name="document_type"]').val();
            }

            if (!documentNumber) {
                documentNumber = $('[name="shippingAddress.custom_attributes.document_number"]').val() ||
                                $('[name="billingAddress.custom_attributes.document_number"]').val() ||
                                $('[name="document_number"]').val();
            }

            // Validate document number length before proceeding
            if (documentNumber && documentNumber.length > 11) {
                errorProcessor.process({
                    message: 'El número de documento no puede exceder 11 caracteres'
                });
                return;
            }

            var url = urlBuilder.build('profar/quote/save');

            if (documentType && documentNumber) {
                var payload = {
                    cartId: quoteId,
                    document_type: documentType,
                    document_number: documentNumber,
                    is_customer: isCustomer
                };

                $.ajax({
                    url: url,
                    data: payload,
                    type: 'POST',
                    dataType: 'json'
                }).fail(function (response) {
                    errorProcessor.process(response);
                });
            }

            return originalAction(paymentData, messageContainer);
        });
    };
});
