<?xml version="1.0"?>
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
      xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
        <referenceContainer name="form.additional.info">
            <block class="Magento\Framework\View\Element\Template" 
                   name="customer_address_document_fields" 
                   template="Profar_CheckoutFields::customer/address/edit.phtml" />
        </referenceContainer>
    </body>
</page>
