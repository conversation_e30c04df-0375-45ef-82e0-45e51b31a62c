<?php
/**
 * @var \Magento\Customer\Block\Address\Edit $block
 */
$address = $block->getAddress();
?>

<script type="text/x-magento-init">
{
    "*": {
        "Profar_CheckoutFields/js/admin/address-form": {}
    }
}
</script>

<div class="field document_type">
    <label class="label" for="document_type">
        <span><?= $block->escapeHtml(__('Document Type')) ?></span>
    </label>
    <div class="control">
        <select name="document_type" id="document_type" class="select">
            <option value=""><?= $block->escapeHtml(__('Please select')) ?></option>
            <option value="rut" <?= $address->getCustomAttribute('document_type') && $address->getCustomAttribute('document_type')->getValue() == 'rut' ? 'selected="selected"' : '' ?>>
                <?= $block->escapeHtml(__('RUT')) ?>
            </option>
        </select>
    </div>
</div>

<div class="field document_number">
    <label class="label" for="document_number">
        <span><?= $block->escapeHtml(__('Document Number')) ?></span>
    </label>
    <div class="control">
        <input type="text" 
               name="document_number" 
               id="document_number" 
               value="<?= $block->escapeHtmlAttr($address->getCustomAttribute('document_number') ? $address->getCustomAttribute('document_number')->getValue() : '') ?>"
               class="input-text"
               maxlength="11" />
    </div>
</div>
