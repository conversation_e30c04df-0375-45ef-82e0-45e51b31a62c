<?php

namespace Profar\CheckoutFields\Block\Customer\Address;

use Magento\Framework\View\Element\Template;
use Magento\Framework\View\Element\Template\Context;
use Magento\Customer\Api\AddressRepositoryInterface;
use Magento\Customer\Model\Session;
use Magento\Framework\Exception\NoSuchEntityException;

class Edit extends Template
{
    protected $addressRepository;
    protected $customerSession;

    public function __construct(
        Context $context,
        AddressRepositoryInterface $addressRepository,
        Session $customerSession,
        array $data = []
    ) {
        $this->addressRepository = $addressRepository;
        $this->customerSession = $customerSession;
        parent::__construct($context, $data);
    }

    /**
     * Get current address being edited
     *
     * @return \Magento\Customer\Api\Data\AddressInterface|null
     */
    public function getAddress()
    {
        $addressId = $this->getRequest()->getParam('id');
        if ($addressId) {
            try {
                return $this->addressRepository->getById($addressId);
            } catch (NoSuchEntityException $e) {
                return null;
            }
        }
        return null;
    }

    /**
     * Get document type value
     *
     * @return string
     */
    public function getDocumentType()
    {
        $address = $this->getAddress();
        if ($address && $address->getCustomAttribute('document_type')) {
            return $address->getCustomAttribute('document_type')->getValue();
        }
        return '';
    }

    /**
     * Get document number value
     *
     * @return string
     */
    public function getDocumentNumber()
    {
        $address = $this->getAddress();
        if ($address && $address->getCustomAttribute('document_number')) {
            return $address->getCustomAttribute('document_number')->getValue();
        }
        return '';
    }

    /**
     * Get document type options
     *
     * @return array
     */
    public function getDocumentTypeOptions()
    {
        return [
            '' => __('Please select'),
            'rut' => __('RUT')
        ];
    }
}
