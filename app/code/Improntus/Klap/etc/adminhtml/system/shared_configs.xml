<?xml version="1.0" ?>
<!--
Copyright © Improntus All rights reserved.
See COPYING.txt for license details.
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Store:etc/config.xsd">
        <field id="sandbox_success_webhook" type="text" sortOrder="18" showInWebsite="1" showInStore="1"
               showInDefault="1" translate="label">
            <label>Success Webhook Address</label>
            <depends>
                <field id="sandbox">1</field>
            </depends>
        </field>
    <field id="sandbox_validation_webhook" type="text" sortOrder="16" showInWebsite="1" showInStore="1"
           showInDefault="1" translate="label">
        <label>Availability Webhook Address</label>
        <depends>
            <field id="sandbox">1</field>
        </depends>
    </field>
    <field id="sandbox_error_webhook" type="text" sortOrder="17" showInWebsite="1" showInStore="1"
           showInDefault="1" translate="label">
        <label>Error Webhook Address</label>
        <depends>
            <field id="sandbox">1</field>
        </depends>
    </field>
    <field id="logo" translate="label" sortOrder="140"
           type="Magento\Config\Block\System\Config\Form\Field\Image" showInDefault="1" showInWebsite="1"
           showInStore="1">
        <label>Logo</label>
        <backend_model>Improntus\Klap\Model\Config\Backend\Image</backend_model>
        <base_url type="media" scope_info="1">klap</base_url>
        <validate>required-entry</validate>
        <upload_dir config="system" scope_info="1">klap</upload_dir>
        <comment><![CDATA[Allowed file types: jpg, jpeg, gif, png, svg]]></comment>
        <depends>
            <field id="active">1</field>
        </depends>
    </field>
</config>
