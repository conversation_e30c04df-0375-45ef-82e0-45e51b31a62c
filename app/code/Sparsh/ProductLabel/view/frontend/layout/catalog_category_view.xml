<?xml version="1.0"?>
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
        <referenceBlock name="category.products.list">
            <action method="setTemplate">
                <argument name="template" xsi:type="string">Sparsh_ProductLabel::product/list.phtml</argument>
            </action>
            <action method="addAttribute">
                <argument name="attribute" xsi:type="string">product_label_background_color</argument>
            </action>
            <action method="addAttribute">
                <argument name="attribute" xsi:type="string">product_label_color</argument>
            </action>
        </referenceBlock>
    </body>
</page>
