<?xml version="1.0"?>
<!-- Ensures Edit Address uses the module template that includes "Comuna" and loads our JS -->
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <update handle="customer_account"/>
    <head>
        <script src="KnownOnline_ChileAddress::js/address-edit-chile.js"/>
    </head>
    <body>
        <referenceContainer name="content">
            <block class="Magento\Customer\Block\Address\Edit"
                   name="customer_address_edit"
                   template="KnownOnline_ChileAddress::address/edit.phtml"
                   cacheable="false">
                <arguments>
                    <argument name="attribute_data" xsi:type="object">Magento\Customer\Block\DataProviders\AddressAttributeData</argument>
                    <argument name="post_code_config" xsi:type="object">Magento\Customer\Block\DataProviders\PostCodesPatternsAttributeData</argument>
                    <argument name="view_model" xsi:type="object">Magento\Customer\ViewModel\Address</argument>
                    <argument name="region_provider" xsi:type="object">Magento\Customer\ViewModel\Address\RegionProvider</argument>
                </arguments>
            </block>
        </referenceContainer>
    </body>
</page>
