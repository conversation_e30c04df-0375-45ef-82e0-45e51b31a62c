<?php
namespace KnownOnline\ChileAddress\Controller\Comuna;

use Magento\Framework\App\Action\Action;
use Magento\Framework\App\Action\Context;
use Magento\Framework\Controller\Result\JsonFactory;
use KnownOnline\ChileAddress\Model\ResourceModel\Comuna\CollectionFactory as ComunaCollectionFactory;
use Magento\Framework\App\CacheInterface;

class Index extends Action
{
    const CACHE_TAG = 'chile_comunas';
    const CACHE_LIFETIME = 86400; // 24 horas

    protected $resultJsonFactory;
    protected $comunaCollectionFactory;
    protected $cache;

    public function __construct(
        Context $context,
        JsonFactory $resultJsonFactory,
        ComunaCollectionFactory $comunaCollectionFactory,
        CacheInterface $cache
    ) {
        parent::__construct($context);
        $this->resultJsonFactory = $resultJsonFactory;
        $this->comunaCollectionFactory = $comunaCollectionFactory;
        $this->cache = $cache;
    }

    public function execute()
    {
        $regionId = (int)$this->getRequest()->getParam('region_id');
        if (!$regionId) {
            return $this->resultJsonFactory->create()->setData([]);
        }

        $cacheKey = 'chile_comunas_region_' . $regionId;
        $cachedData = $this->cache->load($cacheKey);

        if ($cachedData) {
            $data = json_decode($cachedData, true);
        } else {
            $collection = $this->comunaCollectionFactory->create();
            $collection->addFieldToSelect(['comuna_id', 'name', 'codigo_postal'])
                       ->addFieldToFilter('region_id', $regionId)
                       ->setOrder('name', 'ASC');

            $data = $collection->getData();

            // Guardar en caché
            $this->cache->save(
                json_encode($data),
                $cacheKey,
                [self::CACHE_TAG, 'chile_comunas_region'],
                self::CACHE_LIFETIME
            );
        }

        $result = $this->resultJsonFactory->create();
        return $result->setData($data);
    }
}