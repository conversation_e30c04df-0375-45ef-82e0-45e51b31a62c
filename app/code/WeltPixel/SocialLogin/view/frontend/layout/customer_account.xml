<?xml version="1.0"?>
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
        <referenceBlock name="customer_account_navigation">
            <block class="Magento\Framework\View\Element\Html\Link\Current" name="social-account">
                <arguments>
                    <argument name="path" xsi:type="string">sociallogin/account/socialaccounts</argument>
                    <argument name="label" xsi:type="string">My Social Accounts</argument>
                    <argument name="sortOrder" xsi:type="number">50</argument>
                </arguments>
            </block>
        </referenceBlock>
        <referenceBlock name="social-account" remove="true"/>

    </body>
</page>