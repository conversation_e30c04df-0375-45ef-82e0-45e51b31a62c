<?xml version="1.0"?>
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
        <referenceContainer name="before.body.end">
            <block class="WeltPixel\SocialLogin\Block\ButtonDataProvider"
                   name="sl-login_buttons"
                   as="sl-login_buttons"
                   template="WeltPixel_SocialLogin::buttons.phtml">
                <action method="getContent">
                    <argument name="flag" xsi:type="string">socialloginButtons</argument>
                </action>
            </block>
        </referenceContainer>

        <referenceContainer name="content">
            <referenceBlock name="authentication-popup">
                <arguments>
                    <argument name="jsLayout" xsi:type="array">
                        <item name="components" xsi:type="array">
                            <item name="authenticationPopup" xsi:type="array">
                                <item name="component" xsi:type="string">WeltPixel_SocialLogin/js/view/authentication-popup</item>
                            </item>
                        </item>
                    </argument>
                </arguments>
            </referenceBlock>
        </referenceContainer>
    </body>
</page>
