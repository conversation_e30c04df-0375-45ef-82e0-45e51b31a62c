{"name": "paradoxlabs/tokenbase", "description": "Base module for ParadoxLabs tokenization payment methods.", "require": {"magento/framework": "^102.0||^103.0", "magento/module-admin-notification": "^100.0", "magento/module-backend": "^100.0||^101.0||^102.0", "magento/module-checkout": "^100.0", "magento/module-config": "^100.0||^101.0", "magento/module-customer": "^100.0||^101.0||^102.0||^103.0", "magento/module-directory": "^100.0", "magento/module-payment": "^100.0", "magento/module-quote": "^100.0||^101.0", "magento/module-sales": "^100.0||^101.0||^102.0||^103.0", "magento/module-store": "^100.0||^101.0", "monolog/monolog": "^1.11||^2.0", "ext-json": "*"}, "type": "magento2-module", "version": "4.5.1", "time": "2022-04-13", "license": "proprietary", "autoload": {"files": ["registration.php"], "psr-4": {"ParadoxLabs\\TokenBase\\": ""}}}