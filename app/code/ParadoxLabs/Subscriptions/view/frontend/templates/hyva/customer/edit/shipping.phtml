<?php /** @var \ParadoxLabs\Subscriptions\Block\Customer\Edit\Shipping $block */ ?>
<?php /* @codingStandardsIgnoreFile */ ?>
<?php $region = $block->getAddress()->getRegion() ? $block->getAddress()->getRegion()->getRegion() : ''; ?>

<div class="fieldset" id="shipping_address"
     x-data="initShippingAddressEdit()"
     @private-content-loaded.window="onPrivateContentLoaded(event.detail.data)">
    <legend class="legend"><span><?= $block->escapeHtml(__('Shipping Address')) ?></span></legend><br>

    <?php $addresses = $block->getCustomer()->getAddresses(); ?>
    <?php $subscriptionAddress = $block->getSubscription()->getQuote()->getShippingAddress(); ?>
    <?php if (!empty($addresses)): ?>
        <div class="field w-full shipping-address-id do-not-toggle required">
            <label class="label" for="shipping_address_id"><span><?= $block->escapeHtml(__('Shipping Address')) ?></span></label>
            <div class="control">
                <select id="shipping_address_id" name="shipping[address_id]" title="<?= $block->escapeHtmlAttr(__('Shipping Address')) ?>" class="form-select w-full" x-model="selectedId" @change="setInitialRegionSelectValue()">
                    <option value=""><?= $block->escapeHtml(__('Enter below')) ?></option>
                    <?php /** @var \Magento\Customer\Api\Data\AddressInterface $address  */ ?>
                    <?php $selectedAddressId = null; ?>
                    <?php foreach ($addresses as $address): ?>
                        <?php if ($this->helper(\ParadoxLabs\TokenBase\Helper\Address::class)->compareAddresses($address, $subscriptionAddress)): ?>
                            <?php $selectedAddressId = $address->getId() ?>
                        <?php endif; ?>
                        <option value="<?= (int)$address->getId(); ?>"<?php if ($selectedAddressId === $address->getId()): ?> selected="selected"<?php endif; ?>><?= $block->escapeHtml($block->getFormattedAddress($address, 'flat')); ?></option>
                    <?php endforeach; ?>
                </select>
            </div>
        </div>
    <?php endif; ?>

    <div x-show="!(selectedId > 0)">
        <div class="lg:flex lg:flex-row">
            <?= $block->getNameBlockHtml() ?>
        </div>
        <div class="field w-full company">
            <label class="label" for="shipping_company"><span><?= $block->escapeHtml(__('Company')) ?></span></label>
            <div class="control">
                <input type="text" name="shipping[company]" id="shipping_company" title="<?= $block->escapeHtmlAttr(__('Company')) ?>" value="<?= $block->escapeHtmlAttr($block->getAddress()->getCompany()) ?>" class="form-input w-full <?= $block->escapeHtmlAttr($this->helper(\Magento\Customer\Helper\Address::class)->getAttributeValidationClass('company')) ?>">
            </div>
        </div>
        <?php $phoneValidation = $this->helper(\Magento\Customer\Helper\Address::class)->getAttributeValidationClass('telephone'); ?>
        <div class="field w-full telephone<?php if (strpos($phoneValidation, 'required') !== false): ?> required<?php endif; ?>">
            <label class="label" for="shipping_telephone"><span><?= $block->escapeHtml(__('Phone Number')) ?></span></label>
            <div class="control">
                <input type="text" name="shipping[telephone]" value="<?= $block->escapeHtmlAttr($block->getAddress()->getTelephone()) ?>" title="<?= $block->escapeHtmlAttr(__('Phone Number')) ?>" class="form-input w-full <?= $block->escapeHtmlAttr($phoneValidation) ?>" id="shipping_telephone">
            </div>
        </div>
        <div class="field w-full fax">
            <label class="label" for="shipping_fax"><span><?= $block->escapeHtml(__('Fax')) ?></span></label>
            <div class="control">
                <input type="text" name="shipping[fax]" id="shipping_fax" title="<?= $block->escapeHtmlAttr(__('Fax')) ?>" value="<?= $block->escapeHtmlAttr($block->getAddress()->getFax()) ?>" class="form-input w-full <?= $block->escapeHtmlAttr($this->helper(\Magento\Customer\Helper\Address::class)->getAttributeValidationClass('fax')) ?>">
            </div>
        </div>
        <?php $_streetValidationClass = $block->escapeHtmlAttr($this->helper(\Magento\Customer\Helper\Address::class)->getAttributeValidationClass('street')); ?>
        <div class="field w-full street required">
            <label for="shipping_street_1" class="label"><span><?= $block->escapeHtml(__('Street Address')) ?></span></label>
            <div class="control">
                <input type="text" name="shipping[street][]" value="<?= $block->escapeHtmlAttr($block->getStreetLine(1)) ?>" title="<?= $block->escapeHtmlAttr(__('Street Address')) ?>" id="shipping_street_1" class="form-input w-full <?= $_streetValidationClass ?>"  />
                <div class="nested">
                    <?php $_streetValidationClass = trim(str_replace('required-entry', '', $_streetValidationClass)); ?>
                    <?php for ($_i = 1, $_n = $this->helper(\Magento\Customer\Helper\Address::class)->getStreetLines(); $_i < $_n; $_i++): ?>
                        <?php $lineNo = ($_i+1); ?>
                        <div class="field w-full additional">
                            <label class="label" for="shipping_street_<?= $lineNo ?>">
                                <span><?= $block->escapeHtml(__('Street Address %1', $lineNo)) ?></span>
                            </label>
                            <div class="control">
                                <input type="text" name="shipping[street][]" value="<?= $block->escapeHtmlAttr($block->getStreetLine($lineNo)) ?>" title="<?= $block->escapeHtmlAttr(__('Street Address %1', $lineNo)) ?>" id="shipping_street_<?= $lineNo ?>" class="form-input w-full <?= $_streetValidationClass ?>">
                            </div>
                        </div>
                    <?php endfor; ?>
                </div>
            </div>
        </div>

        <?php if ($this->helper(\Magento\Customer\Helper\Address::class)->isVatAttributeVisible()) : ?>
            <div class="field w-full taxvat">
                <label class="label" for="shipping_vat_id"><span><?= $block->escapeHtml(__('VAT Number')) ?></span></label>
                <div class="control">
                    <input type="text" name="shipping[vat_id]" value="<?= $block->escapeHtmlAttr($block->getAddress()->getVatId()) ?>" title="<?= $block->escapeHtmlAttr(__('VAT Number')) ?>" class="form-input w-full <?= $block->escapeHtmlAttr($this->helper(\Magento\Customer\Helper\Address::class)->getAttributeValidationClass('vat_id')) ?>" id="shipping_vat_id">
                </div>
            </div>
        <?php endif; ?>
        <div class="field w-full city required">
            <label class="label" for="shipping_city"><span><?= $block->escapeHtml(__('City')) ?></span></label>
            <div class="control">
                <input type="text" name="shipping[city]" value="<?= $block->escapeHtmlAttr($block->getAddress()->getCity()) ?>" title="<?= $block->escapeHtmlAttr(__('City')) ?>" class="form-input w-full <?= $block->escapeHtmlAttr($this->helper(\Magento\Customer\Helper\Address::class)->getAttributeValidationClass('city')) ?>" id="shipping_city">
            </div>
        </div>
        <div class="field region w-full"
             x-cloak
             x-show="isRegionRequired || showOptionalRegion"
        >
            <label class="label" for="region_id">
                <span><?= $block->escapeHtml(__('State/Province')) ?></span>
            </label>
            <div class="control">
                <template x-if="hasAvailableRegions() && isRegionRequired">
                    <select id="shipping_region_id" name="shipping[region_id]"
                            title="<?= $block->escapeHtml(__('State/Province')) ?>"
                            class="form-select validate-select region_id w-full"
                            :required="isRegionRequired && !(selectedId > 0)"
                            x-ref="region_id"
                            x-model="selectedRegion"
                    >
                        <option value=""><?= $escaper->escapeHtml(__('Please select a region, state or province.')) ?></option>
                        <template x-for="regionId in Object.keys(availableRegions)">
                            <option :value="regionId"
                                    :name="availableRegions[regionId].name"
                                    x-text="availableRegions[regionId].name"
                                    :selected="selectedRegion === regionId"
                            >
                            </option>
                        </template>
                    </select>
                </template>
                <template x-if="!hasAvailableRegions()">
                    <input type="text"
                           id="shipping_region"
                           name="shipping[region]"
                           x-ref="region"
                           value="<?= $escaper->escapeHtmlAttr($region) ?>"
                           title="<?= $block->escapeHtml(__('State/Province')) ?>"
                           class="form-input w-full"
                           :required="isRegionRequired && !(selectedId > 0)"
                    />
                </template>
            </div>
        </div>
        <div class="field w-full zip required">
            <label class="label" for="shipping_zip"><span><?= $block->escapeHtml(__('Zip/Postal Code')) ?></span></label>
            <div class="control">
                <input type="text" name="shipping[postcode]" value="<?= $block->escapeHtmlAttr($block->getAddress()->getPostcode()) ?>" title="<?= $block->escapeHtmlAttr(__('Zip/Postal Code')) ?>" id="shipping_zip" class="form-input w-full validate-zip-international <?= $block->escapeHtmlAttr($this->helper(\Magento\Customer\Helper\Address::class)->getAttributeValidationClass('postcode')) ?>">
            </div>
        </div>
        <div class="field w-full country required">
            <label class="label" for="shipping_country"><span><?= $block->escapeHtml(__('Country')) ?></span></label>
            <div class="control">
                <?php $countries = $block
                    ->getCountryCollection()
                    ->setForegroundCountries($block->getTopDestinations())
                    ->toOptionArray();
                ?>
                <select name="shipping[country_id]"
                        id="shipping_country"
                        title="Country"
                        :required="!(selectedId > 0)"
                        class="form-select w-full"
                        x-ref="country_id"
                        @change="changeCountry($event.target)">
                    <?php foreach ($countries as $country): ?>
                        <option name="<?= /** @noEscape */ $country['label'] ?>"
                                value="<?= /** @noEscape */ $country['value'] ?>"
                                data-is-zip-required="<?= (isset($country['is_zipcode_optional'])) ? '0' : '1' ?>"
                                data-is-region-required="<?= (isset($country['is_region_required'])) ? '1' : '0' ?>"
                                <?= ($block->getAddress()->getCountryId() ===  $country['value']) ? 'selected="selected"' : '' ?>>
                            <?= /** @noEscape */ $country['label'] ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
        </div>
    </div>
</div>

<script>
    function initShippingAddressEdit() {
        return {
            directoryData: {},
            availableRegions: {},
            selectedRegion: '<?= $block->escapeJs($region ?: 0) ?>',
            isZipRequired: true,
            isRegionRequired: true,
            selectedId: <?= $selectedAddressId ?? 'null' ?>,
            showOptionalRegion: <?= $block->getConfig('general/region/display_all') ? 'true': 'false' ?>,
            onPrivateContentLoaded(data) {
                this.directoryData = data['directory-data'] || {};

                <?php if ($block->getAddress()->getCountryId()): ?>
                this.changeCountry(this.$refs['country_id']);
                <?php endif; ?>

                this.setInitialRegionSelectValue();
                this.setRegionInputValue('<?= $block->escapeHtmlAttr($region) ?>');

            },
            setInitialRegionSelectValue(value) {
                this.$nextTick(() => {
                    const regionSelectorElement = this.$refs['region_id'];
                    if (regionSelectorElement) {

                        const regionOptionByValue = regionSelectorElement
                            .querySelector("[name='" + this.selectedRegion + "']");

                        if (regionOptionByValue) {
                            this.$refs['region_id'].value = regionOptionByValue.value;
                        }
                    }
                })
            },
            setRegionInputValue(value) {
                this.$nextTick(() => {
                    const regionInputElement = this.$refs['region'];
                    if (regionInputElement) {
                        regionInputElement.value = value;
                    }
                })
            },
            changeCountry(countrySelectElement) {
                const selectedOption = countrySelectElement.options[countrySelectElement.selectedIndex];
                const countryCode = countrySelectElement.value;
                const countryData = this.directoryData[countryCode] || false;

                this.setRegionInputValue('');

                if (!countryData) {
                    return;
                }

                this.isZipRequired = selectedOption.dataset.isZipRequired === "1";
                this.isRegionRequired = selectedOption.dataset.isRegionRequired === "1";

                this.availableRegions = countryData.regions || {};
            },
            hasAvailableRegions() {
                return Object.keys(this.availableRegions).length > 0;
            }
        }
    }
</script>
